---
export interface Category {
  id: string;
  name: string;
  count?: number;
}

export interface Props {
  categories: Category[];
  activeCategory?: string;
}

const {
  categories,
  activeCategory = 'all'
} = Astro.props;
---

<section class="py-8 bg-white border-b border-primary-100">
  <div class="container">

    
    <!-- Category Navigation -->
    <div class="relative">
      <!-- Scroll container -->
      <div class="overflow-x-auto scrollbar-hide" id="categoryScroll">
        <div class="flex gap-2 pb-2 min-w-max">
          {categories.map((category) => (
            <button 
              class={`category-tab flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all whitespace-nowrap ${
                activeCategory === category.id 
                  ? 'bg-accent-600 text-white shadow-md' 
                  : 'bg-primary-50 text-primary-700 hover:bg-primary-100 hover:text-primary-900'
              }`}
              data-category={category.id}
            >
              {category.name}
              {category.count && (
                <span class={`text-xs px-2 py-0.5 rounded-full ${
                  activeCategory === category.id 
                    ? 'bg-white/20 text-white' 
                    : 'bg-primary-200 text-primary-600'
                }`}>
                  {category.count}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>
      
      <!-- Scroll buttons -->
      <button 
        class="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-2 w-8 h-8 bg-white shadow-lg rounded-full flex items-center justify-center text-primary-600 hover:text-primary-900 transition-all opacity-0 pointer-events-none"
        id="scrollLeft"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>
      
      <button 
        class="absolute right-0 top-1/2 -translate-y-1/2 translate-x-2 w-8 h-8 bg-white shadow-lg rounded-full flex items-center justify-center text-primary-600 hover:text-primary-900 transition-all opacity-0 pointer-events-none"
        id="scrollRight"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>
  </div>
</section>

<style>
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const scrollContainer = document.getElementById('categoryScroll');
    const scrollLeftBtn = document.getElementById('scrollLeft');
    const scrollRightBtn = document.getElementById('scrollRight');
    const categoryTabs = document.querySelectorAll('.category-tab');

    if (!scrollContainer || !scrollLeftBtn || !scrollRightBtn) return;

    // Check if scrolling is needed
    function updateScrollButtons() {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainer;
      
      if (scrollWidth > clientWidth) {
        scrollLeftBtn.style.opacity = scrollLeft > 0 ? '1' : '0';
        scrollLeftBtn.style.pointerEvents = scrollLeft > 0 ? 'auto' : 'none';
        
        scrollRightBtn.style.opacity = scrollLeft < scrollWidth - clientWidth ? '1' : '0';
        scrollRightBtn.style.pointerEvents = scrollLeft < scrollWidth - clientWidth ? 'auto' : 'none';
      } else {
        scrollLeftBtn.style.opacity = '0';
        scrollLeftBtn.style.pointerEvents = 'none';
        scrollRightBtn.style.opacity = '0';
        scrollRightBtn.style.pointerEvents = 'none';
      }
    }

    // Scroll functions
    scrollLeftBtn.addEventListener('click', () => {
      scrollContainer.scrollBy({ left: -200, behavior: 'smooth' });
    });

    scrollRightBtn.addEventListener('click', () => {
      scrollContainer.scrollBy({ left: 200, behavior: 'smooth' });
    });

    // Update buttons on scroll
    scrollContainer.addEventListener('scroll', updateScrollButtons);
    window.addEventListener('resize', updateScrollButtons);
    
    // Initial check
    updateScrollButtons();

    // Category tab click handlers
    categoryTabs.forEach(tab => {
      tab.addEventListener('click', (e) => {
        const categoryId = e.currentTarget.dataset.category;
        console.log('🎯 Category clicked:', categoryId);

        // Remove active class from all tabs
        categoryTabs.forEach(t => {
          t.classList.remove('bg-accent-600', 'text-white', 'shadow-md');
          t.classList.add('bg-primary-50', 'text-primary-700');
        });

        // Add active class to clicked tab
        e.currentTarget.classList.remove('bg-primary-50', 'text-primary-700');
        e.currentTarget.classList.add('bg-accent-600', 'text-white', 'shadow-md');

        // Dispatch custom event for filtering
        console.log('📡 Dispatching categoryChange event:', categoryId);
        window.dispatchEvent(new CustomEvent('categoryChange', {
          detail: { categoryId }
        }));
      });
    });
  });
</script>
