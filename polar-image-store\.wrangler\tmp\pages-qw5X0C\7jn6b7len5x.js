// <define:__ROUTES__>
var define_ROUTES_default = {
  version: 1,
  include: [
    "/*"
  ],
  exclude: [
    "/",
    "/_astro/*",
    "/favicon.svg",
    "/logo.svg",
    "/og-image.jpg",
    "/placeholder-image.svg",
    "/robots.txt",
    "/about",
    "/privacy",
    "/products/*",
    "/success",
    "/terms"
  ]
};

// node_modules/wrangler/templates/pages-dev-pipeline.ts
import worker from "D:\\code\\image\\polar-image-store\\.wrangler\\tmp\\pages-qw5X0C\\bundledWorker-0.****************.mjs";
import { isRoutingRuleMatch } from "D:\\code\\image\\polar-image-store\\node_modules\\wrangler\\templates\\pages-dev-util.ts";
export * from "D:\\code\\image\\polar-image-store\\.wrangler\\tmp\\pages-qw5X0C\\bundledWorker-0.****************.mjs";
var routes = define_ROUTES_default;
var pages_dev_pipeline_default = {
  fetch(request, env, context) {
    const { pathname } = new URL(request.url);
    for (const exclude of routes.exclude) {
      if (isRoutingRuleMatch(pathname, exclude)) {
        return env.ASSETS.fetch(request);
      }
    }
    for (const include of routes.include) {
      if (isRoutingRuleMatch(pathname, include)) {
        const workerAsHandler = worker;
        if (workerAsHandler.fetch === void 0) {
          throw new TypeError("Entry point missing `fetch` handler");
        }
        return workerAsHandler.fetch(request, env, context);
      }
    }
    return env.ASSETS.fetch(request);
  }
};
export {
  pages_dev_pipeline_default as default
};
//# sourceMappingURL=7jn6b7len5x.js.map
