<!DOCTYPE html><html lang="en"> <head><title>Terms of Service - Polar Image Store</title><meta charset="UTF-8"><link rel="canonical" href="https://infpik.store/terms/"><meta name="description" content="Beautiful digital images from our collection"><meta name="robots" content="index, follow"><meta property="og:title" content="Terms of Service - Polar Image Store"><meta property="og:type" content="website"><meta property="og:image" content="http://infpik.store/og-image.jpg"><meta property="og:url" content="https://infpik.store/terms/"><meta property="og:description" content="Beautiful digital images from our collection"><meta property="og:locale" content="en_US"><meta property="og:site_name" content="Polar Image Store"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content="@polarimagestore"><meta name="twitter:title" content="Terms of Service - Polar Image Store"><meta name="twitter:image" content="http://infpik.store/og-image.jpg"><meta name="twitter:image:alt" content="Terms of Service - Polar Image Store - Polar Image Store"><meta name="twitter:description" content="Beautiful digital images from our collection"><meta name="twitter:creator" content="@polarimagestore"><link rel="icon" type="image/svg+xml" href="/favicon.svg"><link rel="sitemap" href="/sitemap-index.xml"><link rel="canonical" href="https://infpik.store/terms/"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="Astro v5.12.4"><meta name="robots" content="index, follow"><meta name="googlebot" content="index, follow"><meta name="theme-color" content="#6366f1"><meta name="msapplication-TileColor" content="#6366f1"><link rel="stylesheet" href="/_astro/about.H3LGoODG.css"></head> <body class="min-h-screen flex flex-col"> <header class="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-primary-100 py-4"> <div class="container"> <nav class="flex items-center justify-between"> <!-- Logo --> <a href="/" class="flex items-center gap-2 text-xl font-bold text-primary-900 hover:text-accent-600 transition-colors"> <img src="/logo.svg" alt="Logo" loading="lazy" decoding="async" fetchpriority="auto" width="32" height="32" class="w-8 h-8 text-accent-600">
InfPik
</a> <!-- Mobile Search Bar --> <div class="md:hidden relative flex-1 mx-2"> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"> <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> <input type="text" id="mobileProductSearch" class="block w-full pl-8 pr-4 py-1.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200 text-sm" placeholder="Search products..." autocomplete="off"> </div> <!-- Search Bar (Desktop) --> <div class="hidden md:flex flex-1 max-w-md mx-8"> <div class="relative w-full"> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"> <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> <input type="text" id="productSearch" class="block w-full pl-10 pr-4 py-2.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200" placeholder="Search products..." autocomplete="off"> <!-- Search results dropdown (hidden by default) --> <div id="searchResults" class="absolute top-full left-0 right-0 mt-1 bg-white border border-primary-200 rounded-xl shadow-lg z-50 hidden max-h-96 overflow-y-auto"> <!-- Search results will be populated here --> </div> </div> </div> <!-- CTA Button & Mobile Menu --> <div class="flex items-center gap-4"> <a href="/products" class="btn-primary hidden md:inline-flex">
Browse Collection
</a> <!-- Mobile menu button --> <button class="md:hidden p-2 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full transition-all" id="mobile-menu-button"> <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path> </svg> </button> </div> </nav> <!-- Mobile menu --> <div class="md:hidden hidden" id="mobile-menu"> <div class="pt-4 pb-2 border-t border-primary-100 mt-4"> <!-- Mobile Navigation --> <ul class="space-y-2"> <li><a href="/" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Home</a></li> <li><a href="/products" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Products</a></li> <li><a href="/about" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">About</a></li> </ul> <!-- Legal Links --> <div class="mt-4 pt-4 border-t border-primary-100"> <p class="px-4 text-xs uppercase text-primary-500 font-medium mb-2">Legal</p> <ul class="space-y-2"> <li><a href="/privacy" class="block py-2 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all text-sm">Privacy Policy</a></li> <li><a href="/terms" class="block py-2 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all text-sm">Terms of Service</a></li> </ul> </div> <div class="mt-4 pt-4 border-t border-primary-100"> <a href="/products" class="btn-primary w-full justify-center">
Browse Collection
</a> </div> </div> </div> </div> </header> <main class="flex-1 pb-12">  <div class="container py-12"> <div class="max-w-4xl mx-auto"> <h1 class="text-4xl font-bold text-primary-900 mb-8">Terms of Service</h1> <div class="prose prose-lg max-w-none"> <p class="lead text-xl text-primary-700 mb-6">
Last Updated: July 30, 2025
</p> <p>
Welcome to Polar Image Store. These Terms of Service ("Terms") govern your access to and use of our website, products, and services. By accessing or using our services, you agree to be bound by these Terms. If you do not agree to these Terms, please do not use our services.
</p> <h2>1. Purchasing Products</h2> <p>
Our website does not require account registration. All purchases are processed through Polar.sh, our payment processor. When making a purchase, you agree to provide accurate, current, and complete information as required for the transaction.
</p> <p>
You are responsible for all purchases made through your browser session. If you believe there has been an unauthorized transaction, please contact us immediately.
</p> <h2>2. License and Usage Rights</h2> <p>
When you purchase digital images from Polar Image Store, we grant you a non-exclusive, non-transferable license to use the purchased content according to the specific license type acquired:
</p> <h3>Standard License</h3> <p>The Standard License permits you to:</p> <ul> <li>Use the image for personal or commercial projects</li> <li>Incorporate the image into websites, social media, presentations, and digital publications</li> <li>Use the image in marketing materials, advertisements, and promotional content</li> <li>Make modifications to the image to suit your project needs</li> </ul> <p>Restrictions under the Standard License:</p> <ul> <li>You may not redistribute or resell the image as a standalone file</li> <li>You may not use the image in products for resale where the image is the primary value</li> <li>You may not use the image in a way that is defamatory, pornographic, or otherwise unlawful</li> <li>Usage is limited to a maximum of 10,000 impressions/views</li> </ul> <h3>Extended License</h3> <p>The Extended License includes all Standard License rights plus:</p> <ul> <li>Unlimited impressions/views</li> <li>Permission to use the image in products for resale</li> <li>Permission to use the image in templates or applications for redistribution</li> </ul> <h2>3. Prohibited Uses</h2> <p>Regardless of the license purchased, you may not:</p> <ul> <li>Use the images to create or promote content that is illegal, harmful, or offensive</li> <li>Claim ownership or authorship of any images purchased from our platform</li> <li>Use the images in a way that infringes upon the rights of others</li> <li>Remove or alter any copyright notices or attributions associated with the images</li> <li>Use the images in a trademark, logo, or service mark without additional permission</li> </ul> <h2>4. Payment and Refunds</h2> <p>
All purchases are processed securely through Polar.sh, our payment processor. Prices are listed in the currency specified on the product page and are subject to change without notice. By completing a purchase, you agree to Polar.sh's terms of service in addition to our own.
</p> <p>
Due to the digital nature of our products, all sales are final and non-refundable unless the product is demonstrably defective or corrupted. Claims for defective products must be submitted within 7 days of purchase by contacting us directly.
</p> <h2>5. Intellectual Property</h2> <p>
All content on our website, including images, text, graphics, logos, and software, is the property of Polar Image Store or our content suppliers and is protected by copyright, trademark, and other intellectual property laws.
</p> <p>
While we grant you certain usage rights to purchased content, we (or our contributors) retain all ownership rights to the images. Your purchase of a license does not transfer copyright or ownership of the content to you.
</p> <h2>6. Communications</h2> <p>
When you contact us via email or other communication channels, you grant us permission to use the content of your communications for customer service and quality improvement purposes. We do not currently offer features for submitting reviews, comments, or other public content on our website.
</p> <h2>7. Limitation of Liability</h2> <p>
To the maximum extent permitted by law, Polar Image Store shall not be liable for any indirect, incidental, special, consequential, or punitive damages, or any loss of profits or revenues, whether incurred directly or indirectly, or any loss of data, use, goodwill, or other intangible losses resulting from your access to or use of or inability to access or use the services.
</p> <h2>8. Indemnification</h2> <p>
You agree to indemnify, defend, and hold harmless Polar Image Store and our officers, directors, employees, agents, and affiliates from and against any claims, disputes, demands, liabilities, damages, losses, and costs and expenses, including, without limitation, reasonable legal and accounting fees, arising out of or in any way connected with your access to or use of the services or your violation of these Terms.
</p> <h2>9. Service Restrictions</h2> <p>
We reserve the right to restrict your access to our services at our sole discretion, without prior notice or liability, for any reason, including, without limitation, if you breach these Terms. Since we do not offer user accounts, such restrictions may be implemented through technical measures such as IP blocking if necessary.
</p> <h2>10. Changes to Terms</h2> <p>
We reserve the right to modify these Terms at any time. If we make changes, we will provide notice by posting the updated Terms on our website and updating the "Last Updated" date. Your continued use of our services after such changes constitutes your acceptance of the new Terms.
</p> <h2>11. Governing Law</h2> <p>
These Terms shall be governed by and construed in accordance with the laws of the jurisdiction in which Polar Image Store is established, without regard to its conflict of law provisions.
</p> <h2>12. Contact Us</h2> <p>
If you have any questions about these Terms, please contact us at:
</p> <p> <a href="mailto:<EMAIL>" class="text-accent-600 hover:text-accent-700"><EMAIL></a> </p> </div> </div> </div>  </main> <footer class="bg-white border-t border-primary-100 py-12 text-primary-600"> <div class="container"> <div class="text-center"> <div class="flex items-center justify-center gap-2 text-lg font-semibold text-primary-900 mb-4"> <img src="/logo.svg" alt="Logo" loading="lazy" decoding="async" fetchpriority="auto" width="24" height="24" class="w-6 h-6 text-accent-600">
InfPik
</div> <div class="flex justify-center gap-4 mb-4"> <a href="/about" class="text-sm hover:text-accent-600 transition-colors">About Us</a> <a href="/privacy" class="text-sm hover:text-accent-600 transition-colors">Privacy Policy</a> <a href="/terms" class="text-sm hover:text-accent-600 transition-colors">Terms of Service</a> </div> <p class="text-sm">&copy; 2025 Polar Image Store. All rights reserved.</p> </div> </div> </footer> <script type="module">document.addEventListener("DOMContentLoaded",()=>{const o=document.getElementById("mobile-menu-button"),c=document.getElementById("mobile-menu");o&&c&&o.addEventListener("click",()=>{c.classList.toggle("hidden")});const i=document.getElementById("productSearch"),d=document.getElementById("mobileProductSearch"),e=document.getElementById("searchResults");let a;async function m(t){const s=t.value.trim();a&&clearTimeout(a),s.length>2?e&&(e.classList.remove("hidden"),e.innerHTML=`
            <div class="p-4 text-center text-primary-600">
              <div class="flex items-center justify-center gap-2">
                <svg class="animate-spin w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span class="text-sm">Searching for "${s}"...</span>
              </div>
            </div>
          `,a=setTimeout(async()=>{try{const r=await(await fetch(`/api/search?q=${encodeURIComponent(s)}`)).json();if(e&&!e.classList.contains("hidden"))if(r.results&&r.results.length>0){const u=r.results.map(n=>`
                    <a href="/products/${n.slug}" class="block p-3 hover:bg-primary-50 rounded-lg transition-colors border-b border-primary-100 last:border-b-0">
                      <div class="flex items-center gap-3">
                        ${n.image?`<img src="${n.image}" alt="${n.name}" class="w-10 h-10 object-cover rounded-lg">`:""}
                        <div class="flex-1 min-w-0">
                          <div class="text-primary-900 font-medium truncate">${n.name}</div>
                          <div class="text-primary-600 text-sm truncate">${n.description}</div>
                          <div class="text-accent-600 text-sm font-semibold">$${n.price}</div>
                        </div>
                      </div>
                    </a>
                  `).join("");e.innerHTML=`
                    <div class="p-2">
                      <div class="text-xs text-primary-500 px-3 py-2 border-b border-primary-100">
                        Found ${r.total} result${r.total===1?"":"s"}
                      </div>
                      ${u}
                      <div class="p-3 border-t border-primary-100">
                        <a href="/products?search=${encodeURIComponent(s)}" class="block text-center text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors">
                          View all ${r.total} results →
                        </a>
                      </div>
                    </div>
                  `}else e.innerHTML=`
                    <div class="p-4 text-center">
                      <div class="text-primary-600 mb-2">No results found for "${s}"</div>
                      <a href="/products" class="text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors">
                        Browse all products →
                      </a>
                    </div>
                  `}catch(l){console.error("Search error:",l),e&&!e.classList.contains("hidden")&&(e.innerHTML=`
                  <div class="p-4 text-center text-red-600">
                    <div class="text-sm">Search failed. Please try again.</div>
                  </div>
                `)}},300)):e&&e.classList.add("hidden")}i&&(i.addEventListener("input",t=>m(t.target)),i.addEventListener("keydown",t=>{if(t.key==="Enter"){t.preventDefault();const s=t.target.value.trim();s&&(window.location.href=`/products?search=${encodeURIComponent(s)}`)}})),d&&d.addEventListener("keydown",t=>{if(t.key==="Enter"){t.preventDefault();const s=t.target.value.trim();s&&(window.location.href=`/products?search=${encodeURIComponent(s)}`)}}),document.addEventListener("click",t=>{e&&!i?.contains(t.target)&&!e.contains(t.target)&&e.classList.add("hidden")})});</script></body></html>