globalThis.process ??= {}; globalThis.process.env ??= {};
import { P as Polar } from './sdk_DEQ9AU5A.mjs';

function createPolarClient() {
  const accessToken = "polar_oat_ouH54pn5flleg2O77vuEz0JdL0plgG6L9u74d4QLM5Z";
  return new Polar({
    accessToken,
    server: "production"
    // Always use production
  });
}
function transformPolarProduct(polarProduct) {
  if (!polarProduct || !polarProduct.id || !polarProduct.name) {
    console.warn("Invalid polar product:", polarProduct);
    return null;
  }
  const firstPrice = polarProduct.prices?.[0];
  const price = firstPrice?.priceAmount || 0;
  const currency = firstPrice?.priceCurrency || "USD";
  const category = polarProduct.metadata?.category || null;
  return {
    id: polarProduct.id,
    name: polarProduct.name,
    description: polarProduct.description || "",
    price: price / 100,
    // Convert from cents to dollars
    currency,
    images: polarProduct.medias?.map((media) => media.publicUrl) || [],
    slug: generateSlug(polarProduct.name),
    isAvailable: !polarProduct.isArchived,
    tags: extractTags(polarProduct.description || ""),
    category,
    createdAt: polarProduct.createdAt,
    updatedAt: polarProduct.modifiedAt || polarProduct.createdAt
  };
}
function generateSlug(name) {
  if (!name || typeof name !== "string") {
    return "";
  }
  return name.toLowerCase().replace(/[^a-z0-9]+/g, "-").replace(/^-+|-+$/g, "");
}
function extractTags(description) {
  const tagRegex = /#(\w+)/g;
  const matches = description.match(tagRegex);
  return matches ? matches.map((tag) => tag.slice(1)) : [];
}
function formatPrice(price, currency = "USD") {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency.toUpperCase()
  }).format(price);
}
function getProductsByCategory(products, category) {
  if (category === "all") {
    return products;
  }
  return products.filter((product) => product.category === category);
}
function getCategoryDisplayName(category) {
  return category.split("-").map((word) => word.charAt(0).toUpperCase() + word.slice(1)).join(" ");
}
function generateCategoriesWithCounts(products) {
  const categoryMap = /* @__PURE__ */ new Map();
  products.forEach((product) => {
    if (product.category) {
      const count = categoryMap.get(product.category) || 0;
      categoryMap.set(product.category, count + 1);
    }
  });
  const categories = Array.from(categoryMap.entries()).map(([id, count]) => ({
    id,
    name: getCategoryDisplayName(id),
    count
  }));
  categories.sort((a, b) => a.name.localeCompare(b.name));
  categories.unshift({
    id: "all",
    name: "All",
    count: products.length
  });
  return categories;
}

export { generateCategoriesWithCounts as a, getCategoryDisplayName as b, createPolarClient as c, formatPrice as f, getProductsByCategory as g, transformPolarProduct as t };
