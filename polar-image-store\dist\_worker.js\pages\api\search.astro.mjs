globalThis.process ??= {}; globalThis.process.env ??= {};
import { c as createPolarClient, t as transformPolarProduct } from '../../chunks/polar_COtxeXrO.mjs';
export { renderers } from '../../renderers.mjs';

const prerender = false;
const GET = async ({ url }) => {
  try {
    const query = url.searchParams.get("q");
    if (!query || query.trim().length < 2) {
      return new Response(
        JSON.stringify({ results: [] }),
        {
          status: 200,
          headers: {
            "Content-Type": "application/json",
            "Cache-Control": "public, max-age=60"
            // Cache for 1 minute
          }
        }
      );
    }
    const polar = createPolarClient();
    const organizationId = "e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";
    if (!organizationId) ;
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });
    const productList = response.result?.items || [];
    const allProducts = productList.map(transformPolarProduct).filter((product) => product !== null);
    const searchQuery = query.toLowerCase().trim();
    const filteredProducts = allProducts.filter((product) => {
      return product.name.toLowerCase().includes(searchQuery) || product.description.toLowerCase().includes(searchQuery) || product.tags && product.tags.some((tag) => tag.toLowerCase().includes(searchQuery));
    });
    const results = filteredProducts.slice(0, 5).map((product) => ({
      id: product.id,
      name: product.name,
      slug: product.slug,
      price: product.price,
      currency: product.currency,
      image: product.images[0] || null,
      description: product.description.substring(0, 100) + (product.description.length > 100 ? "..." : "")
    }));
    return new Response(
      JSON.stringify({
        results,
        total: filteredProducts.length,
        query
      }),
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "public, max-age=60"
          // Cache for 1 minute
        }
      }
    );
  } catch (error) {
    console.error("Search API error:", error);
    return new Response(
      JSON.stringify({ error: "Search failed" }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
};

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET,
  prerender
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
