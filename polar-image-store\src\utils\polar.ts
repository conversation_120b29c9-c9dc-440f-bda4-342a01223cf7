import { Polar } from '@polar-sh/sdk';
import type { LocalProduct } from '../types/polar';

// Initialize Polar client
export function createPolarClient() {
  const accessToken = import.meta.env.POLAR_ACCESS_TOKEN;

  if (!accessToken) {
    throw new Error('POLAR_ACCESS_TOKEN is required');
  }

  return new Polar({
    accessToken,
    server: 'production' // Always use production
  });
}

// Convert Polar product to local product format
export function transformPolarProduct(polarProduct: any): LocalProduct | null {
  if (!polarProduct || !polarProduct.id || !polarProduct.name) {
    console.warn('Invalid polar product:', polarProduct);
    return null;
  }

  const firstPrice = polarProduct.prices?.[0];
  const price = firstPrice?.priceAmount || 0;
  const currency = firstPrice?.priceCurrency || 'USD';

  // Extract category from metadata
  const category = polarProduct.metadata?.category || null;

  return {
    id: polarProduct.id,
    name: polarProduct.name,
    description: polarProduct.description || '',
    price: price / 100, // Convert from cents to dollars
    currency,
    images: polarProduct.medias?.map((media: any) => media.publicUrl) || [],
    slug: generateSlug(polarProduct.name),
    isAvailable: !polarProduct.isArchived,
    tags: extractTags(polarProduct.description || ''),
    category,
    createdAt: polarProduct.createdAt,
    updatedAt: polarProduct.modifiedAt || polarProduct.createdAt
  };
}

// Generate URL-friendly slug from product name
export function generateSlug(name: string): string {
  if (!name || typeof name !== 'string') {
    return '';
  }

  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

// Extract tags from description (simple implementation)
export function extractTags(description: string): string[] {
  const tagRegex = /#(\w+)/g;
  const matches = description.match(tagRegex);
  return matches ? matches.map(tag => tag.slice(1)) : [];
}

// Format price for display
export function formatPrice(price: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase()
  }).format(price);
}

// Create checkout URL
export async function createCheckoutUrl(productId: string): Promise<string> {
  const polar = createPolarClient();

  try {
    const checkoutLink = await polar.checkoutLinks.create({
      paymentProcessor: 'stripe',
      productId,
      allowDiscountCodes: true,
      requireBillingAddress: false,
      successUrl: `${import.meta.env.PUBLIC_SITE_URL}/success`
    });

    return checkoutLink.url;
  } catch (error) {
    console.error('Failed to create checkout URL:', error);
    throw new Error('Unable to create checkout URL');
  }
}

// Extract unique categories from products
export function extractCategories(products: LocalProduct[]): string[] {
  const categories = products
    .map(product => product.category)
    .filter((category): category is string => Boolean(category))
    .filter((category, index, array) => array.indexOf(category) === index) // Remove duplicates
    .sort();

  return categories;
}

// Get products by category
export function getProductsByCategory(products: LocalProduct[], category: string): LocalProduct[] {
  if (category === 'all') {
    return products;
  }

  return products.filter(product => product.category === category);
}

// Get category display name (convert slug to readable name)
export function getCategoryDisplayName(category: string): string {
  return category
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Generate categories with counts for navigation
export function generateCategoriesWithCounts(products: LocalProduct[]): Array<{id: string, name: string, count: number}> {
  const categoryMap = new Map<string, number>();

  // Count products in each category
  products.forEach(product => {
    if (product.category) {
      const count = categoryMap.get(product.category) || 0;
      categoryMap.set(product.category, count + 1);
    }
  });

  // Convert to array and add "All" category
  const categories = Array.from(categoryMap.entries()).map(([id, count]) => ({
    id,
    name: getCategoryDisplayName(id),
    count
  }));

  // Sort by name
  categories.sort((a, b) => a.name.localeCompare(b.name));

  // Add "All" category at the beginning
  categories.unshift({
    id: 'all',
    name: 'All',
    count: products.length
  });

  return categories;
}
