globalThis.process ??= {}; globalThis.process.env ??= {};
import { P as Polar } from './sdk_DEQ9AU5A.mjs';

function createPolarClient() {
  const accessToken = "polar_oat_ouH54pn5flleg2O77vuEz0JdL0plgG6L9u74d4QLM5Z";
  return new Polar({
    accessToken,
    server: "production"
    // Always use production
  });
}
function transformPolarProduct(polarProduct) {
  if (!polarProduct || !polarProduct.id || !polarProduct.name) {
    console.warn("Invalid polar product:", polarProduct);
    return null;
  }
  const firstPrice = polarProduct.prices?.[0];
  const price = firstPrice?.priceAmount || 0;
  const currency = firstPrice?.priceCurrency || "USD";
  const category = polarProduct.metadata?.category || null;
  const metadataTags = polarProduct.metadata?.tags ? polarProduct.metadata.tags.split(",").map((tag) => tag.trim()) : [];
  const descriptionTags = extractTags(polarProduct.description || "");
  const allTags = [...metadataTags, ...descriptionTags].filter(Boolean);
  return {
    id: polarProduct.id,
    name: polarProduct.name,
    description: polarProduct.description || "",
    price: price / 100,
    // Convert from cents to dollars
    currency,
    images: polarProduct.medias?.map((media) => media.publicUrl) || [],
    slug: generateSlug(polarProduct.name),
    isAvailable: !polarProduct.isArchived,
    tags: allTags,
    category,
    createdAt: polarProduct.createdAt,
    updatedAt: polarProduct.modifiedAt || polarProduct.createdAt
  };
}
function generateSlug(name) {
  if (!name || typeof name !== "string") {
    return "";
  }
  return name.toLowerCase().replace(/[^a-z0-9]+/g, "-").replace(/^-+|-+$/g, "");
}
function extractTags(description) {
  const tagRegex = /#(\w+)/g;
  const matches = description.match(tagRegex);
  return matches ? matches.map((tag) => tag.slice(1)) : [];
}
function formatPrice(price, currency = "USD") {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency.toUpperCase()
  }).format(price);
}
function getProductsByCategory(products, category) {
  if (category === "all") {
    return products;
  }
  return products.filter((product) => product.category === category);
}
function getCategoryDisplayName(category) {
  return category.split("-").map((word) => word.charAt(0).toUpperCase() + word.slice(1)).join(" ");
}
function generateCategoriesWithCounts(products) {
  const categoryMap = /* @__PURE__ */ new Map();
  products.forEach((product) => {
    if (product.category) {
      const count = categoryMap.get(product.category) || 0;
      categoryMap.set(product.category, count + 1);
    }
  });
  const categories = Array.from(categoryMap.entries()).map(([id, count]) => ({
    id,
    name: getCategoryDisplayName(id),
    count
  }));
  categories.sort((a, b) => a.name.localeCompare(b.name));
  categories.unshift({
    id: "all",
    name: "All",
    count: products.length
  });
  return categories;
}
function getProductsByTag(products, tag) {
  if (tag === "all") {
    return products;
  }
  return products.filter(
    (product) => product.tags && product.tags.includes(tag)
  );
}
function getTagDisplayName(tag) {
  return tag.split("-").map((word) => word.charAt(0).toUpperCase() + word.slice(1)).join(" ");
}
function generateTagsWithCounts(products) {
  const tagMap = /* @__PURE__ */ new Map();
  products.forEach((product) => {
    if (product.tags) {
      product.tags.forEach((tag) => {
        const count = tagMap.get(tag) || 0;
        tagMap.set(tag, count + 1);
      });
    }
  });
  const tags = Array.from(tagMap.entries()).map(([id, count]) => ({
    id,
    name: getTagDisplayName(id),
    count
  }));
  tags.sort((a, b) => {
    if (b.count !== a.count) {
      return b.count - a.count;
    }
    return a.name.localeCompare(b.name);
  });
  tags.unshift({
    id: "all",
    name: "All Tags",
    count: products.length
  });
  return tags;
}

export { generateCategoriesWithCounts as a, getCategoryDisplayName as b, createPolarClient as c, getProductsByTag as d, generateTagsWithCounts as e, formatPrice as f, getProductsByCategory as g, getTagDisplayName as h, transformPolarProduct as t };
