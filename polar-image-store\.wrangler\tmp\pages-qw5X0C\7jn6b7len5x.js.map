{"version": 3, "sources": ["<define:__ROUTES__>", "../../../node_modules/wrangler/templates/pages-dev-pipeline.ts"], "sourcesContent": ["{\n  \"version\": 1,\n  \"include\": [\n    \"/*\"\n  ],\n  \"exclude\": [\n    \"/\",\n    \"/_astro/*\",\n    \"/favicon.svg\",\n    \"/logo.svg\",\n    \"/og-image.jpg\",\n    \"/placeholder-image.svg\",\n    \"/robots.txt\",\n    \"/about\",\n    \"/privacy\",\n    \"/products/*\",\n    \"/success\",\n    \"/terms\"\n  ]\n}", "// @ts-ignore entry point will get replaced\nimport worker from \"__ENTRY_POINT__\";\nimport { isRoutingRuleMatch } from \"./pages-dev-util\";\n\n// @ts-ignore entry point will get replaced\nexport * from \"__ENTRY_POINT__\";\n\n// @ts-ignore routes are injected\nconst routes = __ROUTES__;\n\nexport default <ExportedHandler<{ ASSETS: Fetcher }>>{\n\tfetch(request, env, context) {\n\t\tconst { pathname } = new URL(request.url);\n\n\t\tfor (const exclude of routes.exclude) {\n\t\t\tif (isRoutingRuleMatch(pathname, exclude)) {\n\t\t\t\treturn env.ASSETS.fetch(request);\n\t\t\t}\n\t\t}\n\n\t\tfor (const include of routes.include) {\n\t\t\tif (isRoutingRuleMatch(pathname, include)) {\n\t\t\t\tconst workerAsHandler = worker as ExportedHandler;\n\t\t\t\tif (workerAsHandler.fetch === undefined) {\n\t\t\t\t\tthrow new TypeError(\"Entry point missing `fetch` handler\");\n\t\t\t\t}\n\t\t\t\treturn workerAsHandler.fetch(request, env, context);\n\t\t\t}\n\t\t}\n\n\t\treturn env.ASSETS.fetch(request);\n\t},\n};\n"], "mappings": ";AAAA;AAAA,EACE,SAAW;AAAA,EACX,SAAW;AAAA,IACT;AAAA,EACF;AAAA,EACA,SAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;AClBA,OAAO,YAAY;AACnB,SAAS,0BAA0B;AAGnC,cAAc;AAGd,IAAM,SAAS;AAEf,IAAO,6BAA8C;AAAA,EACpD,MAAM,SAAS,KAAK,SAAS;AAC5B,UAAM,EAAE,SAAS,IAAI,IAAI,IAAI,QAAQ,GAAG;AAExC,eAAW,WAAW,OAAO,SAAS;AACrC,UAAI,mBAAmB,UAAU,OAAO,GAAG;AAC1C,eAAO,IAAI,OAAO,MAAM,OAAO;AAAA,MAChC;AAAA,IACD;AAEA,eAAW,WAAW,OAAO,SAAS;AACrC,UAAI,mBAAmB,UAAU,OAAO,GAAG;AAC1C,cAAM,kBAAkB;AACxB,YAAI,gBAAgB,UAAU,QAAW;AACxC,gBAAM,IAAI,UAAU,qCAAqC;AAAA,QAC1D;AACA,eAAO,gBAAgB,MAAM,SAAS,KAAK,OAAO;AAAA,MACnD;AAAA,IACD;AAEA,WAAO,IAAI,OAAO,MAAM,OAAO;AAAA,EAChC;AACD;", "names": []}