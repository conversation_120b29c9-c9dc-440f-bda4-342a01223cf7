---
import Layout from '../../layouts/Layout.astro';
import ProductCard from '../../components/ProductCard.astro';
import CategoryNavigation from '../../components/CategoryNavigation.astro';
import StructuredData from '../../components/StructuredData.astro';
import { createPolarClient, transformPolarProduct, generateCategoriesWithCounts, getProductsByCategory } from '../../utils/polar';
import type { LocalProduct } from '../../types/polar';

let products: LocalProduct[] = [];
let allProducts: LocalProduct[] = [];
let categories: Array<{id: string, name: string, count: number}> = [];
let error: string | null = null;
let searchQuery: string = '';
let selectedCategory: string = '';

// Get search query and category from URL parameters
const url = new URL(Astro.request.url);
searchQuery = url.searchParams.get('search') || '';
selectedCategory = url.searchParams.get('category') || 'all';



try {
  const polar = createPolarClient();
  const organizationId = import.meta.env.POLAR_ORGANIZATION_ID;

  if (organizationId) {
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });

    const productList = response.result?.items || [];
    allProducts = productList
      .map(transformPolarProduct)
      .filter((product): product is LocalProduct => product !== null);

    // Generate categories from products
    categories = generateCategoriesWithCounts(allProducts);

    // For static site, show all products initially
    // Client-side filtering will handle category/search filtering
    products = allProducts;
  } else {
    error = 'Organization ID not configured';
  }
} catch (e) {
  console.error('Error fetching products:', e);
  error = 'Failed to load products';
}

// Breadcrumb data
const breadcrumbData = {
  items: [
    { name: "Home", url: import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store' },
    { name: "Products", url: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/products` }
  ]
};
---

<Layout
  title="Products - Polar Image Store"
  description="Browse our collection of premium digital images and artwork. High-quality digital assets for creative projects, available for instant download."
  canonical={`${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/products`}
>
  <!-- Breadcrumb Structured Data -->
  <StructuredData type="BreadcrumbList" data={breadcrumbData} />
  <div class="container mx-auto px-4 py-8">
    <section class="text-center mb-12">
      {searchQuery ? (
        <div>
          <h1 class="text-4xl font-bold text-gray-900 mb-4">Search Results</h1>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            {products.length > 0
              ? `Found ${products.length} result${products.length === 1 ? '' : 's'} for "${searchQuery}"`
              : `No results found for "${searchQuery}"`
            }
          </p>
          {products.length === 0 && (
            <div class="mt-6">
              <a
                href="/products"
                class="inline-flex items-center gap-2 px-6 py-3 bg-accent-600 text-white rounded-full font-semibold transition-all duration-200 hover:bg-accent-700 hover:scale-105"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                Browse All Products
              </a>
            </div>
          )}
        </div>
      ) : (
        <div>
          <h1 class="text-4xl font-bold text-gray-900 mb-4">Our Collection</h1>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">Discover beautiful digital images for your projects</p>
        </div>
      )}
    </section>

    <!-- Category Navigation -->
    {!error && categories.length > 1 && (
      <CategoryNavigation categories={categories} activeCategory={selectedCategory} />
    )}

    {error && (
      <div class="bg-red-50 border border-red-200 rounded-xl p-6 mb-8">
        <div class="flex items-center gap-3 text-red-800">
          <svg class="w-6 h-6 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
          </svg>
          <div>
            <p class="font-semibold">⚠️ {error}</p>
            <p class="text-sm">Please check your configuration and try again.</p>
          </div>
        </div>
      </div>
    )}

    {!error && products.length === 0 && (
      <div class="text-center py-16">
        <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
          <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
          </svg>
        </div>
        <h2 class="text-2xl font-semibold text-gray-900 mb-2">No products available</h2>
        <p class="text-gray-600">Check back soon for new additions to our collection!</p>
      </div>
    )}
    
    {!error && products.length > 0 && (
      <div id="products-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {products.map((product) => (
          <div class="product-item" data-category={product.category || 'uncategorized'} data-name={product.name.toLowerCase()} data-description={product.description.toLowerCase()} data-tags={product.tags?.join(',').toLowerCase() || ''}>
            <ProductCard product={product} />
          </div>
        ))}
      </div>
    )}
  </div>
</Layout>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    console.log('📦 Products page script loaded');

    // Get URL parameters for initial filtering
    const urlParams = new URLSearchParams(window.location.search);
    const initialCategory = urlParams.get('category') || 'all';
    const initialSearch = urlParams.get('search') || '';

    console.log('Initial category:', initialCategory);
    console.log('Initial search:', initialSearch);

    // Apply initial filters
    filterProducts(initialCategory, initialSearch);

    // Handle category change events from CategoryNavigation
    window.addEventListener('categoryChange', (event) => {
      const categoryId = event.detail.categoryId;
      console.log('📦 Products page received categoryChange:', categoryId);

      const url = new URL(window.location);

      if (categoryId === 'all') {
        url.searchParams.delete('category');
      } else {
        url.searchParams.set('category', categoryId);
      }

      // Update URL without reload
      window.history.pushState({}, '', url.toString());

      // Apply filtering
      const searchQuery = url.searchParams.get('search') || '';
      filterProducts(categoryId, searchQuery);
    });

    // Filter products function
    function filterProducts(category, search) {
      const productItems = document.querySelectorAll('.product-item');
      let visibleCount = 0;

      productItems.forEach(item => {
        const productCategory = item.dataset.category;
        const productName = item.dataset.name;
        const productDescription = item.dataset.description;
        const productTags = item.dataset.tags;

        let categoryMatch = category === 'all' || productCategory === category;
        let searchMatch = true;

        if (search.trim()) {
          const searchLower = search.toLowerCase();
          searchMatch = productName.includes(searchLower) ||
                       productDescription.includes(searchLower) ||
                       productTags.includes(searchLower);
        }

        if (categoryMatch && searchMatch) {
          item.style.display = 'block';
          visibleCount++;
        } else {
          item.style.display = 'none';
        }
      });

      console.log('Visible products:', visibleCount);

      // Update empty state
      const emptyState = document.querySelector('.empty-state');
      const productsGrid = document.getElementById('products-grid');

      if (visibleCount === 0) {
        if (productsGrid) productsGrid.style.display = 'none';
        if (emptyState) emptyState.style.display = 'block';
      } else {
        if (productsGrid) productsGrid.style.display = 'grid';
        if (emptyState) emptyState.style.display = 'none';
      }
    }
  });
</script>