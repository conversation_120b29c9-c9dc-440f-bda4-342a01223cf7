globalThis.process ??= {}; globalThis.process.env ??= {};
import { o as objectType, B as Benefit$inboundSchema, l as literalType, a as Benefit$outboundSchema, n as nullableType, u as unionType, C as Customer$inboundSchema, s as stringType, b as booleanType, c as BenefitGrantDiscordProperties$inboundSchema, d as BenefitGrantGitHubRepositoryProperties$inboundSchema, e as BenefitGrantDownloadablesProperties$inboundSchema, f as BenefitGrantLicenseKeysProperties$inboundSchema, g as BenefitGrantCustomProperties$inboundSchema, h as BenefitGrantError$inboundSchema, r as remap, i as BenefitGrantDiscordProperties$outboundSchema, j as BenefitGrantGitHubRepositoryProperties$outboundSchema, k as BenefitGrantDownloadablesProperties$outboundSchema, m as BenefitGrantLicenseKeysProperties$outboundSchema, p as BenefitGrantCustomProperties$outboundSchema, q as Customer$outboundSchema, t as dateType, v as BenefitGrantError$outboundSchema, w as Checkout$inboundSchema, x as Checkout$outboundSchema, O as Order$inboundSchema, y as Order$outboundSchema, z as Organization$inboundSchema, A as Organization$outboundSchema, D as Product$inboundSchema, E as Product$outboundSchema, R as Refund$inboundSchema, F as Refund$outboundSchema, S as Subscription$inboundSchema, G as Subscription$outboundSchema, H as CustomerState$inboundSchema, I as CustomerState$outboundSchema, J as SDKValidationError, P as Polar } from './sdk_DEQ9AU5A.mjs';

// src/webhooks/webhooks.ts
var handleWebhookPayload = async (payload, { webhookSecret, entitlements, onPayload, ...eventHandlers }) => {
  const promises = [];
  if (onPayload) {
    promises.push(onPayload(payload));
  }
  switch (payload.type) {
    case "checkout.created":
      if (eventHandlers.onCheckoutCreated) {
        promises.push(eventHandlers.onCheckoutCreated(payload));
      }
      break;
    case "checkout.updated":
      if (eventHandlers.onCheckoutUpdated) {
        promises.push(eventHandlers.onCheckoutUpdated(payload));
      }
      break;
    case "order.created":
      if (eventHandlers.onOrderCreated) {
        promises.push(eventHandlers.onOrderCreated(payload));
      }
      break;
    case "order.updated":
      if (eventHandlers.onOrderUpdated) {
        promises.push(eventHandlers.onOrderUpdated(payload));
      }
      break;
    case "order.paid":
      if (eventHandlers.onOrderPaid) {
        promises.push(eventHandlers.onOrderPaid(payload));
      }
      break;
    case "subscription.created":
      if (eventHandlers.onSubscriptionCreated) {
        promises.push(eventHandlers.onSubscriptionCreated(payload));
      }
      break;
    case "subscription.updated":
      if (eventHandlers.onSubscriptionUpdated) {
        promises.push(eventHandlers.onSubscriptionUpdated(payload));
      }
      break;
    case "subscription.active":
      if (eventHandlers.onSubscriptionActive) {
        promises.push(eventHandlers.onSubscriptionActive(payload));
      }
      break;
    case "subscription.canceled":
      if (eventHandlers.onSubscriptionCanceled) {
        promises.push(eventHandlers.onSubscriptionCanceled(payload));
      }
      break;
    case "subscription.revoked":
      if (eventHandlers.onSubscriptionRevoked) {
        promises.push(eventHandlers.onSubscriptionRevoked(payload));
      }
      break;
    case "product.created":
      if (eventHandlers.onProductCreated) {
        promises.push(eventHandlers.onProductCreated(payload));
      }
      break;
    case "product.updated":
      if (eventHandlers.onProductUpdated) {
        promises.push(eventHandlers.onProductUpdated(payload));
      }
      break;
    case "organization.updated":
      if (eventHandlers.onOrganizationUpdated) {
        promises.push(eventHandlers.onOrganizationUpdated(payload));
      }
      break;
    case "benefit.created":
      if (eventHandlers.onBenefitCreated) {
        promises.push(eventHandlers.onBenefitCreated(payload));
      }
      break;
    case "benefit.updated":
      if (eventHandlers.onBenefitUpdated) {
        promises.push(eventHandlers.onBenefitUpdated(payload));
      }
      break;
    case "benefit_grant.created":
      if (eventHandlers.onBenefitGrantCreated) {
        promises.push(eventHandlers.onBenefitGrantCreated(payload));
      }
      break;
    case "benefit_grant.updated":
      if (eventHandlers.onBenefitGrantUpdated) {
        promises.push(eventHandlers.onBenefitGrantUpdated(payload));
      }
      break;
    case "benefit_grant.revoked":
      if (eventHandlers.onBenefitGrantRevoked) {
        promises.push(eventHandlers.onBenefitGrantRevoked(payload));
      }
      break;
    case "customer.created":
      if (eventHandlers.onCustomerCreated) {
        promises.push(eventHandlers.onCustomerCreated(payload));
      }
      break;
    case "customer.updated":
      if (eventHandlers.onCustomerUpdated) {
        promises.push(eventHandlers.onCustomerUpdated(payload));
      }
      break;
    case "customer.deleted":
      if (eventHandlers.onCustomerDeleted) {
        promises.push(eventHandlers.onCustomerDeleted(payload));
      }
      break;
    case "customer.state_changed":
      if (eventHandlers.onCustomerStateChanged) {
        promises.push(eventHandlers.onCustomerStateChanged(payload));
      }
      break;
  }
  switch (payload.type) {
    case "benefit_grant.created":
    case "benefit_grant.revoked":
      if (entitlements) {
        for (const handler of entitlements.handlers) {
          promises.push(handler(payload));
        }
      }
  }
  return Promise.all(promises);
};

var dist = {};

var timing_safe_equal = {};

var hasRequiredTiming_safe_equal;

function requireTiming_safe_equal () {
	if (hasRequiredTiming_safe_equal) return timing_safe_equal;
	hasRequiredTiming_safe_equal = 1;
	Object.defineProperty(timing_safe_equal, "__esModule", { value: true });
	timing_safe_equal.timingSafeEqual = void 0;
	function assert(expr, msg = "") {
	    if (!expr) {
	        throw new Error(msg);
	    }
	}
	function timingSafeEqual(a, b) {
	    if (a.byteLength !== b.byteLength) {
	        return false;
	    }
	    if (!(a instanceof DataView)) {
	        a = new DataView(ArrayBuffer.isView(a) ? a.buffer : a);
	    }
	    if (!(b instanceof DataView)) {
	        b = new DataView(ArrayBuffer.isView(b) ? b.buffer : b);
	    }
	    assert(a instanceof DataView);
	    assert(b instanceof DataView);
	    const length = a.byteLength;
	    let out = 0;
	    let i = -1;
	    while (++i < length) {
	        out |= a.getUint8(i) ^ b.getUint8(i);
	    }
	    return out === 0;
	}
	timing_safe_equal.timingSafeEqual = timingSafeEqual;
	
	return timing_safe_equal;
}

var base64 = {};

var hasRequiredBase64;

function requireBase64 () {
	if (hasRequiredBase64) return base64;
	hasRequiredBase64 = 1;
	// Copyright (C) 2016 Dmitry Chestnykh
	// MIT License. See LICENSE file for details.
	var __extends = (base64 && base64.__extends) || (function () {
	    var extendStatics = function (d, b) {
	        extendStatics = Object.setPrototypeOf ||
	            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
	            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };
	        return extendStatics(d, b);
	    };
	    return function (d, b) {
	        extendStatics(d, b);
	        function __() { this.constructor = d; }
	        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
	    };
	})();
	Object.defineProperty(base64, "__esModule", { value: true });
	/**
	 * Package base64 implements Base64 encoding and decoding.
	 */
	// Invalid character used in decoding to indicate
	// that the character to decode is out of range of
	// alphabet and cannot be decoded.
	var INVALID_BYTE = 256;
	/**
	 * Implements standard Base64 encoding.
	 *
	 * Operates in constant time.
	 */
	var Coder = /** @class */ (function () {
	    // TODO(dchest): methods to encode chunk-by-chunk.
	    function Coder(_paddingCharacter) {
	        if (_paddingCharacter === void 0) { _paddingCharacter = "="; }
	        this._paddingCharacter = _paddingCharacter;
	    }
	    Coder.prototype.encodedLength = function (length) {
	        if (!this._paddingCharacter) {
	            return (length * 8 + 5) / 6 | 0;
	        }
	        return (length + 2) / 3 * 4 | 0;
	    };
	    Coder.prototype.encode = function (data) {
	        var out = "";
	        var i = 0;
	        for (; i < data.length - 2; i += 3) {
	            var c = (data[i] << 16) | (data[i + 1] << 8) | (data[i + 2]);
	            out += this._encodeByte((c >>> 3 * 6) & 63);
	            out += this._encodeByte((c >>> 2 * 6) & 63);
	            out += this._encodeByte((c >>> 1 * 6) & 63);
	            out += this._encodeByte((c >>> 0 * 6) & 63);
	        }
	        var left = data.length - i;
	        if (left > 0) {
	            var c = (data[i] << 16) | (left === 2 ? data[i + 1] << 8 : 0);
	            out += this._encodeByte((c >>> 3 * 6) & 63);
	            out += this._encodeByte((c >>> 2 * 6) & 63);
	            if (left === 2) {
	                out += this._encodeByte((c >>> 1 * 6) & 63);
	            }
	            else {
	                out += this._paddingCharacter || "";
	            }
	            out += this._paddingCharacter || "";
	        }
	        return out;
	    };
	    Coder.prototype.maxDecodedLength = function (length) {
	        if (!this._paddingCharacter) {
	            return (length * 6 + 7) / 8 | 0;
	        }
	        return length / 4 * 3 | 0;
	    };
	    Coder.prototype.decodedLength = function (s) {
	        return this.maxDecodedLength(s.length - this._getPaddingLength(s));
	    };
	    Coder.prototype.decode = function (s) {
	        if (s.length === 0) {
	            return new Uint8Array(0);
	        }
	        var paddingLength = this._getPaddingLength(s);
	        var length = s.length - paddingLength;
	        var out = new Uint8Array(this.maxDecodedLength(length));
	        var op = 0;
	        var i = 0;
	        var haveBad = 0;
	        var v0 = 0, v1 = 0, v2 = 0, v3 = 0;
	        for (; i < length - 4; i += 4) {
	            v0 = this._decodeChar(s.charCodeAt(i + 0));
	            v1 = this._decodeChar(s.charCodeAt(i + 1));
	            v2 = this._decodeChar(s.charCodeAt(i + 2));
	            v3 = this._decodeChar(s.charCodeAt(i + 3));
	            out[op++] = (v0 << 2) | (v1 >>> 4);
	            out[op++] = (v1 << 4) | (v2 >>> 2);
	            out[op++] = (v2 << 6) | v3;
	            haveBad |= v0 & INVALID_BYTE;
	            haveBad |= v1 & INVALID_BYTE;
	            haveBad |= v2 & INVALID_BYTE;
	            haveBad |= v3 & INVALID_BYTE;
	        }
	        if (i < length - 1) {
	            v0 = this._decodeChar(s.charCodeAt(i));
	            v1 = this._decodeChar(s.charCodeAt(i + 1));
	            out[op++] = (v0 << 2) | (v1 >>> 4);
	            haveBad |= v0 & INVALID_BYTE;
	            haveBad |= v1 & INVALID_BYTE;
	        }
	        if (i < length - 2) {
	            v2 = this._decodeChar(s.charCodeAt(i + 2));
	            out[op++] = (v1 << 4) | (v2 >>> 2);
	            haveBad |= v2 & INVALID_BYTE;
	        }
	        if (i < length - 3) {
	            v3 = this._decodeChar(s.charCodeAt(i + 3));
	            out[op++] = (v2 << 6) | v3;
	            haveBad |= v3 & INVALID_BYTE;
	        }
	        if (haveBad !== 0) {
	            throw new Error("Base64Coder: incorrect characters for decoding");
	        }
	        return out;
	    };
	    // Standard encoding have the following encoded/decoded ranges,
	    // which we need to convert between.
	    //
	    // ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 0123456789  +   /
	    // Index:   0 - 25                    26 - 51              52 - 61   62  63
	    // ASCII:  65 - 90                    97 - 122             48 - 57   43  47
	    //
	    // Encode 6 bits in b into a new character.
	    Coder.prototype._encodeByte = function (b) {
	        // Encoding uses constant time operations as follows:
	        //
	        // 1. Define comparison of A with B using (A - B) >>> 8:
	        //          if A > B, then result is positive integer
	        //          if A <= B, then result is 0
	        //
	        // 2. Define selection of C or 0 using bitwise AND: X & C:
	        //          if X == 0, then result is 0
	        //          if X != 0, then result is C
	        //
	        // 3. Start with the smallest comparison (b >= 0), which is always
	        //    true, so set the result to the starting ASCII value (65).
	        //
	        // 4. Continue comparing b to higher ASCII values, and selecting
	        //    zero if comparison isn't true, otherwise selecting a value
	        //    to add to result, which:
	        //
	        //          a) undoes the previous addition
	        //          b) provides new value to add
	        //
	        var result = b;
	        // b >= 0
	        result += 65;
	        // b > 25
	        result += ((25 - b) >>> 8) & ((0 - 65) - 26 + 97);
	        // b > 51
	        result += ((51 - b) >>> 8) & ((26 - 97) - 52 + 48);
	        // b > 61
	        result += ((61 - b) >>> 8) & ((52 - 48) - 62 + 43);
	        // b > 62
	        result += ((62 - b) >>> 8) & ((62 - 43) - 63 + 47);
	        return String.fromCharCode(result);
	    };
	    // Decode a character code into a byte.
	    // Must return 256 if character is out of alphabet range.
	    Coder.prototype._decodeChar = function (c) {
	        // Decoding works similar to encoding: using the same comparison
	        // function, but now it works on ranges: result is always incremented
	        // by value, but this value becomes zero if the range is not
	        // satisfied.
	        //
	        // Decoding starts with invalid value, 256, which is then
	        // subtracted when the range is satisfied. If none of the ranges
	        // apply, the function returns 256, which is then checked by
	        // the caller to throw error.
	        var result = INVALID_BYTE; // start with invalid character
	        // c == 43 (c > 42 and c < 44)
	        result += (((42 - c) & (c - 44)) >>> 8) & (-INVALID_BYTE + c - 43 + 62);
	        // c == 47 (c > 46 and c < 48)
	        result += (((46 - c) & (c - 48)) >>> 8) & (-INVALID_BYTE + c - 47 + 63);
	        // c > 47 and c < 58
	        result += (((47 - c) & (c - 58)) >>> 8) & (-INVALID_BYTE + c - 48 + 52);
	        // c > 64 and c < 91
	        result += (((64 - c) & (c - 91)) >>> 8) & (-INVALID_BYTE + c - 65 + 0);
	        // c > 96 and c < 123
	        result += (((96 - c) & (c - 123)) >>> 8) & (-INVALID_BYTE + c - 97 + 26);
	        return result;
	    };
	    Coder.prototype._getPaddingLength = function (s) {
	        var paddingLength = 0;
	        if (this._paddingCharacter) {
	            for (var i = s.length - 1; i >= 0; i--) {
	                if (s[i] !== this._paddingCharacter) {
	                    break;
	                }
	                paddingLength++;
	            }
	            if (s.length < 4 || paddingLength > 2) {
	                throw new Error("Base64Coder: incorrect padding");
	            }
	        }
	        return paddingLength;
	    };
	    return Coder;
	}());
	base64.Coder = Coder;
	var stdCoder = new Coder();
	function encode(data) {
	    return stdCoder.encode(data);
	}
	base64.encode = encode;
	function decode(s) {
	    return stdCoder.decode(s);
	}
	base64.decode = decode;
	/**
	 * Implements URL-safe Base64 encoding.
	 * (Same as Base64, but '+' is replaced with '-', and '/' with '_').
	 *
	 * Operates in constant time.
	 */
	var URLSafeCoder = /** @class */ (function (_super) {
	    __extends(URLSafeCoder, _super);
	    function URLSafeCoder() {
	        return _super !== null && _super.apply(this, arguments) || this;
	    }
	    // URL-safe encoding have the following encoded/decoded ranges:
	    //
	    // ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 0123456789  -   _
	    // Index:   0 - 25                    26 - 51              52 - 61   62  63
	    // ASCII:  65 - 90                    97 - 122             48 - 57   45  95
	    //
	    URLSafeCoder.prototype._encodeByte = function (b) {
	        var result = b;
	        // b >= 0
	        result += 65;
	        // b > 25
	        result += ((25 - b) >>> 8) & ((0 - 65) - 26 + 97);
	        // b > 51
	        result += ((51 - b) >>> 8) & ((26 - 97) - 52 + 48);
	        // b > 61
	        result += ((61 - b) >>> 8) & ((52 - 48) - 62 + 45);
	        // b > 62
	        result += ((62 - b) >>> 8) & ((62 - 45) - 63 + 95);
	        return String.fromCharCode(result);
	    };
	    URLSafeCoder.prototype._decodeChar = function (c) {
	        var result = INVALID_BYTE;
	        // c == 45 (c > 44 and c < 46)
	        result += (((44 - c) & (c - 46)) >>> 8) & (-INVALID_BYTE + c - 45 + 62);
	        // c == 95 (c > 94 and c < 96)
	        result += (((94 - c) & (c - 96)) >>> 8) & (-INVALID_BYTE + c - 95 + 63);
	        // c > 47 and c < 58
	        result += (((47 - c) & (c - 58)) >>> 8) & (-INVALID_BYTE + c - 48 + 52);
	        // c > 64 and c < 91
	        result += (((64 - c) & (c - 91)) >>> 8) & (-INVALID_BYTE + c - 65 + 0);
	        // c > 96 and c < 123
	        result += (((96 - c) & (c - 123)) >>> 8) & (-INVALID_BYTE + c - 97 + 26);
	        return result;
	    };
	    return URLSafeCoder;
	}(Coder));
	base64.URLSafeCoder = URLSafeCoder;
	var urlSafeCoder = new URLSafeCoder();
	function encodeURLSafe(data) {
	    return urlSafeCoder.encode(data);
	}
	base64.encodeURLSafe = encodeURLSafe;
	function decodeURLSafe(s) {
	    return urlSafeCoder.decode(s);
	}
	base64.decodeURLSafe = decodeURLSafe;
	base64.encodedLength = function (length) {
	    return stdCoder.encodedLength(length);
	};
	base64.maxDecodedLength = function (length) {
	    return stdCoder.maxDecodedLength(length);
	};
	base64.decodedLength = function (s) {
	    return stdCoder.decodedLength(s);
	};
	
	return base64;
}

var sha256$1 = {exports: {}};

var sha256 = sha256$1.exports;

var hasRequiredSha256;

function requireSha256 () {
	if (hasRequiredSha256) return sha256$1.exports;
	hasRequiredSha256 = 1;
	(function (module) {
		(function (root, factory) {
		    // Hack to make all exports of this module sha256 function object properties.
		    var exports = {};
		    factory(exports);
		    var sha256 = exports["default"];
		    for (var k in exports) {
		        sha256[k] = exports[k];
		    }
		        
		    {
		        module.exports = sha256;
		    }
		})(sha256, function(exports) {
		exports.__esModule = true;
		// SHA-256 (+ HMAC and PBKDF2) for JavaScript.
		//
		// Written in 2014-2016 by Dmitry Chestnykh.
		// Public domain, no warranty.
		//
		// Functions (accept and return Uint8Arrays):
		//
		//   sha256(message) -> hash
		//   sha256.hmac(key, message) -> mac
		//   sha256.pbkdf2(password, salt, rounds, dkLen) -> dk
		//
		//  Classes:
		//
		//   new sha256.Hash()
		//   new sha256.HMAC(key)
		//
		exports.digestLength = 32;
		exports.blockSize = 64;
		// SHA-256 constants
		var K = new Uint32Array([
		    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b,
		    0x59f111f1, 0x923f82a4, 0xab1c5ed5, 0xd807aa98, 0x12835b01,
		    0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7,
		    0xc19bf174, 0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc,
		    0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da, 0x983e5152,
		    0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147,
		    0x06ca6351, 0x14292967, 0x27b70a85, 0x2e1b2138, 0x4d2c6dfc,
		    0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,
		    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819,
		    0xd6990624, 0xf40e3585, 0x106aa070, 0x19a4c116, 0x1e376c08,
		    0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f,
		    0x682e6ff3, 0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208,
		    0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2
		]);
		function hashBlocks(w, v, p, pos, len) {
		    var a, b, c, d, e, f, g, h, u, i, j, t1, t2;
		    while (len >= 64) {
		        a = v[0];
		        b = v[1];
		        c = v[2];
		        d = v[3];
		        e = v[4];
		        f = v[5];
		        g = v[6];
		        h = v[7];
		        for (i = 0; i < 16; i++) {
		            j = pos + i * 4;
		            w[i] = (((p[j] & 0xff) << 24) | ((p[j + 1] & 0xff) << 16) |
		                ((p[j + 2] & 0xff) << 8) | (p[j + 3] & 0xff));
		        }
		        for (i = 16; i < 64; i++) {
		            u = w[i - 2];
		            t1 = (u >>> 17 | u << (32 - 17)) ^ (u >>> 19 | u << (32 - 19)) ^ (u >>> 10);
		            u = w[i - 15];
		            t2 = (u >>> 7 | u << (32 - 7)) ^ (u >>> 18 | u << (32 - 18)) ^ (u >>> 3);
		            w[i] = (t1 + w[i - 7] | 0) + (t2 + w[i - 16] | 0);
		        }
		        for (i = 0; i < 64; i++) {
		            t1 = (((((e >>> 6 | e << (32 - 6)) ^ (e >>> 11 | e << (32 - 11)) ^
		                (e >>> 25 | e << (32 - 25))) + ((e & f) ^ (~e & g))) | 0) +
		                ((h + ((K[i] + w[i]) | 0)) | 0)) | 0;
		            t2 = (((a >>> 2 | a << (32 - 2)) ^ (a >>> 13 | a << (32 - 13)) ^
		                (a >>> 22 | a << (32 - 22))) + ((a & b) ^ (a & c) ^ (b & c))) | 0;
		            h = g;
		            g = f;
		            f = e;
		            e = (d + t1) | 0;
		            d = c;
		            c = b;
		            b = a;
		            a = (t1 + t2) | 0;
		        }
		        v[0] += a;
		        v[1] += b;
		        v[2] += c;
		        v[3] += d;
		        v[4] += e;
		        v[5] += f;
		        v[6] += g;
		        v[7] += h;
		        pos += 64;
		        len -= 64;
		    }
		    return pos;
		}
		// Hash implements SHA256 hash algorithm.
		var Hash = /** @class */ (function () {
		    function Hash() {
		        this.digestLength = exports.digestLength;
		        this.blockSize = exports.blockSize;
		        // Note: Int32Array is used instead of Uint32Array for performance reasons.
		        this.state = new Int32Array(8); // hash state
		        this.temp = new Int32Array(64); // temporary state
		        this.buffer = new Uint8Array(128); // buffer for data to hash
		        this.bufferLength = 0; // number of bytes in buffer
		        this.bytesHashed = 0; // number of total bytes hashed
		        this.finished = false; // indicates whether the hash was finalized
		        this.reset();
		    }
		    // Resets hash state making it possible
		    // to re-use this instance to hash other data.
		    Hash.prototype.reset = function () {
		        this.state[0] = 0x6a09e667;
		        this.state[1] = 0xbb67ae85;
		        this.state[2] = 0x3c6ef372;
		        this.state[3] = 0xa54ff53a;
		        this.state[4] = 0x510e527f;
		        this.state[5] = 0x9b05688c;
		        this.state[6] = 0x1f83d9ab;
		        this.state[7] = 0x5be0cd19;
		        this.bufferLength = 0;
		        this.bytesHashed = 0;
		        this.finished = false;
		        return this;
		    };
		    // Cleans internal buffers and re-initializes hash state.
		    Hash.prototype.clean = function () {
		        for (var i = 0; i < this.buffer.length; i++) {
		            this.buffer[i] = 0;
		        }
		        for (var i = 0; i < this.temp.length; i++) {
		            this.temp[i] = 0;
		        }
		        this.reset();
		    };
		    // Updates hash state with the given data.
		    //
		    // Optionally, length of the data can be specified to hash
		    // fewer bytes than data.length.
		    //
		    // Throws error when trying to update already finalized hash:
		    // instance must be reset to use it again.
		    Hash.prototype.update = function (data, dataLength) {
		        if (dataLength === void 0) { dataLength = data.length; }
		        if (this.finished) {
		            throw new Error("SHA256: can't update because hash was finished.");
		        }
		        var dataPos = 0;
		        this.bytesHashed += dataLength;
		        if (this.bufferLength > 0) {
		            while (this.bufferLength < 64 && dataLength > 0) {
		                this.buffer[this.bufferLength++] = data[dataPos++];
		                dataLength--;
		            }
		            if (this.bufferLength === 64) {
		                hashBlocks(this.temp, this.state, this.buffer, 0, 64);
		                this.bufferLength = 0;
		            }
		        }
		        if (dataLength >= 64) {
		            dataPos = hashBlocks(this.temp, this.state, data, dataPos, dataLength);
		            dataLength %= 64;
		        }
		        while (dataLength > 0) {
		            this.buffer[this.bufferLength++] = data[dataPos++];
		            dataLength--;
		        }
		        return this;
		    };
		    // Finalizes hash state and puts hash into out.
		    //
		    // If hash was already finalized, puts the same value.
		    Hash.prototype.finish = function (out) {
		        if (!this.finished) {
		            var bytesHashed = this.bytesHashed;
		            var left = this.bufferLength;
		            var bitLenHi = (bytesHashed / 0x20000000) | 0;
		            var bitLenLo = bytesHashed << 3;
		            var padLength = (bytesHashed % 64 < 56) ? 64 : 128;
		            this.buffer[left] = 0x80;
		            for (var i = left + 1; i < padLength - 8; i++) {
		                this.buffer[i] = 0;
		            }
		            this.buffer[padLength - 8] = (bitLenHi >>> 24) & 0xff;
		            this.buffer[padLength - 7] = (bitLenHi >>> 16) & 0xff;
		            this.buffer[padLength - 6] = (bitLenHi >>> 8) & 0xff;
		            this.buffer[padLength - 5] = (bitLenHi >>> 0) & 0xff;
		            this.buffer[padLength - 4] = (bitLenLo >>> 24) & 0xff;
		            this.buffer[padLength - 3] = (bitLenLo >>> 16) & 0xff;
		            this.buffer[padLength - 2] = (bitLenLo >>> 8) & 0xff;
		            this.buffer[padLength - 1] = (bitLenLo >>> 0) & 0xff;
		            hashBlocks(this.temp, this.state, this.buffer, 0, padLength);
		            this.finished = true;
		        }
		        for (var i = 0; i < 8; i++) {
		            out[i * 4 + 0] = (this.state[i] >>> 24) & 0xff;
		            out[i * 4 + 1] = (this.state[i] >>> 16) & 0xff;
		            out[i * 4 + 2] = (this.state[i] >>> 8) & 0xff;
		            out[i * 4 + 3] = (this.state[i] >>> 0) & 0xff;
		        }
		        return this;
		    };
		    // Returns the final hash digest.
		    Hash.prototype.digest = function () {
		        var out = new Uint8Array(this.digestLength);
		        this.finish(out);
		        return out;
		    };
		    // Internal function for use in HMAC for optimization.
		    Hash.prototype._saveState = function (out) {
		        for (var i = 0; i < this.state.length; i++) {
		            out[i] = this.state[i];
		        }
		    };
		    // Internal function for use in HMAC for optimization.
		    Hash.prototype._restoreState = function (from, bytesHashed) {
		        for (var i = 0; i < this.state.length; i++) {
		            this.state[i] = from[i];
		        }
		        this.bytesHashed = bytesHashed;
		        this.finished = false;
		        this.bufferLength = 0;
		    };
		    return Hash;
		}());
		exports.Hash = Hash;
		// HMAC implements HMAC-SHA256 message authentication algorithm.
		var HMAC = /** @class */ (function () {
		    function HMAC(key) {
		        this.inner = new Hash();
		        this.outer = new Hash();
		        this.blockSize = this.inner.blockSize;
		        this.digestLength = this.inner.digestLength;
		        var pad = new Uint8Array(this.blockSize);
		        if (key.length > this.blockSize) {
		            (new Hash()).update(key).finish(pad).clean();
		        }
		        else {
		            for (var i = 0; i < key.length; i++) {
		                pad[i] = key[i];
		            }
		        }
		        for (var i = 0; i < pad.length; i++) {
		            pad[i] ^= 0x36;
		        }
		        this.inner.update(pad);
		        for (var i = 0; i < pad.length; i++) {
		            pad[i] ^= 0x36 ^ 0x5c;
		        }
		        this.outer.update(pad);
		        this.istate = new Uint32Array(8);
		        this.ostate = new Uint32Array(8);
		        this.inner._saveState(this.istate);
		        this.outer._saveState(this.ostate);
		        for (var i = 0; i < pad.length; i++) {
		            pad[i] = 0;
		        }
		    }
		    // Returns HMAC state to the state initialized with key
		    // to make it possible to run HMAC over the other data with the same
		    // key without creating a new instance.
		    HMAC.prototype.reset = function () {
		        this.inner._restoreState(this.istate, this.inner.blockSize);
		        this.outer._restoreState(this.ostate, this.outer.blockSize);
		        return this;
		    };
		    // Cleans HMAC state.
		    HMAC.prototype.clean = function () {
		        for (var i = 0; i < this.istate.length; i++) {
		            this.ostate[i] = this.istate[i] = 0;
		        }
		        this.inner.clean();
		        this.outer.clean();
		    };
		    // Updates state with provided data.
		    HMAC.prototype.update = function (data) {
		        this.inner.update(data);
		        return this;
		    };
		    // Finalizes HMAC and puts the result in out.
		    HMAC.prototype.finish = function (out) {
		        if (this.outer.finished) {
		            this.outer.finish(out);
		        }
		        else {
		            this.inner.finish(out);
		            this.outer.update(out, this.digestLength).finish(out);
		        }
		        return this;
		    };
		    // Returns message authentication code.
		    HMAC.prototype.digest = function () {
		        var out = new Uint8Array(this.digestLength);
		        this.finish(out);
		        return out;
		    };
		    return HMAC;
		}());
		exports.HMAC = HMAC;
		// Returns SHA256 hash of data.
		function hash(data) {
		    var h = (new Hash()).update(data);
		    var digest = h.digest();
		    h.clean();
		    return digest;
		}
		exports.hash = hash;
		// Function hash is both available as module.hash and as default export.
		exports["default"] = hash;
		// Returns HMAC-SHA256 of data under the key.
		function hmac(key, data) {
		    var h = (new HMAC(key)).update(data);
		    var digest = h.digest();
		    h.clean();
		    return digest;
		}
		exports.hmac = hmac;
		// Fills hkdf buffer like this:
		// T(1) = HMAC-Hash(PRK, T(0) | info | 0x01)
		function fillBuffer(buffer, hmac, info, counter) {
		    // Counter is a byte value: check if it overflowed.
		    var num = counter[0];
		    if (num === 0) {
		        throw new Error("hkdf: cannot expand more");
		    }
		    // Prepare HMAC instance for new data with old key.
		    hmac.reset();
		    // Hash in previous output if it was generated
		    // (i.e. counter is greater than 1).
		    if (num > 1) {
		        hmac.update(buffer);
		    }
		    // Hash in info if it exists.
		    if (info) {
		        hmac.update(info);
		    }
		    // Hash in the counter.
		    hmac.update(counter);
		    // Output result to buffer and clean HMAC instance.
		    hmac.finish(buffer);
		    // Increment counter inside typed array, this works properly.
		    counter[0]++;
		}
		var hkdfSalt = new Uint8Array(exports.digestLength); // Filled with zeroes.
		function hkdf(key, salt, info, length) {
		    if (salt === void 0) { salt = hkdfSalt; }
		    if (length === void 0) { length = 32; }
		    var counter = new Uint8Array([1]);
		    // HKDF-Extract uses salt as HMAC key, and key as data.
		    var okm = hmac(salt, key);
		    // Initialize HMAC for expanding with extracted key.
		    // Ensure no collisions with `hmac` function.
		    var hmac_ = new HMAC(okm);
		    // Allocate buffer.
		    var buffer = new Uint8Array(hmac_.digestLength);
		    var bufpos = buffer.length;
		    var out = new Uint8Array(length);
		    for (var i = 0; i < length; i++) {
		        if (bufpos === buffer.length) {
		            fillBuffer(buffer, hmac_, info, counter);
		            bufpos = 0;
		        }
		        out[i] = buffer[bufpos++];
		    }
		    hmac_.clean();
		    buffer.fill(0);
		    counter.fill(0);
		    return out;
		}
		exports.hkdf = hkdf;
		// Derives a key from password and salt using PBKDF2-HMAC-SHA256
		// with the given number of iterations.
		//
		// The number of bytes returned is equal to dkLen.
		//
		// (For better security, avoid dkLen greater than hash length - 32 bytes).
		function pbkdf2(password, salt, iterations, dkLen) {
		    var prf = new HMAC(password);
		    var len = prf.digestLength;
		    var ctr = new Uint8Array(4);
		    var t = new Uint8Array(len);
		    var u = new Uint8Array(len);
		    var dk = new Uint8Array(dkLen);
		    for (var i = 0; i * len < dkLen; i++) {
		        var c = i + 1;
		        ctr[0] = (c >>> 24) & 0xff;
		        ctr[1] = (c >>> 16) & 0xff;
		        ctr[2] = (c >>> 8) & 0xff;
		        ctr[3] = (c >>> 0) & 0xff;
		        prf.reset();
		        prf.update(salt);
		        prf.update(ctr);
		        prf.finish(u);
		        for (var j = 0; j < len; j++) {
		            t[j] = u[j];
		        }
		        for (var j = 2; j <= iterations; j++) {
		            prf.reset();
		            prf.update(u).finish(u);
		            for (var k = 0; k < len; k++) {
		                t[k] ^= u[k];
		            }
		        }
		        for (var j = 0; j < len && i * len + j < dkLen; j++) {
		            dk[i * len + j] = t[j];
		        }
		    }
		    for (var i = 0; i < len; i++) {
		        t[i] = u[i] = 0;
		    }
		    for (var i = 0; i < 4; i++) {
		        ctr[i] = 0;
		    }
		    prf.clean();
		    return dk;
		}
		exports.pbkdf2 = pbkdf2;
		}); 
	} (sha256$1));
	return sha256$1.exports;
}

var hasRequiredDist;

function requireDist () {
	if (hasRequiredDist) return dist;
	hasRequiredDist = 1;
	Object.defineProperty(dist, "__esModule", { value: true });
	dist.Webhook = dist.WebhookVerificationError = void 0;
	const timing_safe_equal_1 = requireTiming_safe_equal();
	const base64 = requireBase64();
	const sha256 = requireSha256();
	const WEBHOOK_TOLERANCE_IN_SECONDS = 5 * 60;
	class ExtendableError extends Error {
	    constructor(message) {
	        super(message);
	        Object.setPrototypeOf(this, ExtendableError.prototype);
	        this.name = "ExtendableError";
	        this.stack = new Error(message).stack;
	    }
	}
	class WebhookVerificationError extends ExtendableError {
	    constructor(message) {
	        super(message);
	        Object.setPrototypeOf(this, WebhookVerificationError.prototype);
	        this.name = "WebhookVerificationError";
	    }
	}
	dist.WebhookVerificationError = WebhookVerificationError;
	class Webhook {
	    constructor(secret, options) {
	        if (!secret) {
	            throw new Error("Secret can't be empty.");
	        }
	        if ((options === null || options === void 0 ? void 0 : options.format) === "raw") {
	            if (secret instanceof Uint8Array) {
	                this.key = secret;
	            }
	            else {
	                this.key = Uint8Array.from(secret, (c) => c.charCodeAt(0));
	            }
	        }
	        else {
	            if (typeof secret !== "string") {
	                throw new Error("Expected secret to be of type string");
	            }
	            if (secret.startsWith(Webhook.prefix)) {
	                secret = secret.substring(Webhook.prefix.length);
	            }
	            this.key = base64.decode(secret);
	        }
	    }
	    verify(payload, headers_) {
	        const headers = {};
	        for (const key of Object.keys(headers_)) {
	            headers[key.toLowerCase()] = headers_[key];
	        }
	        const msgId = headers["webhook-id"];
	        const msgSignature = headers["webhook-signature"];
	        const msgTimestamp = headers["webhook-timestamp"];
	        if (!msgSignature || !msgId || !msgTimestamp) {
	            throw new WebhookVerificationError("Missing required headers");
	        }
	        const timestamp = this.verifyTimestamp(msgTimestamp);
	        const computedSignature = this.sign(msgId, timestamp, payload);
	        const expectedSignature = computedSignature.split(",")[1];
	        const passedSignatures = msgSignature.split(" ");
	        const encoder = new globalThis.TextEncoder();
	        for (const versionedSignature of passedSignatures) {
	            const [version, signature] = versionedSignature.split(",");
	            if (version !== "v1") {
	                continue;
	            }
	            if ((0, timing_safe_equal_1.timingSafeEqual)(encoder.encode(signature), encoder.encode(expectedSignature))) {
	                return JSON.parse(payload.toString());
	            }
	        }
	        throw new WebhookVerificationError("No matching signature found");
	    }
	    sign(msgId, timestamp, payload) {
	        if (typeof payload === "string") ;
	        else if (payload.constructor.name === "Buffer") {
	            payload = payload.toString();
	        }
	        else {
	            throw new Error("Expected payload to be of type string or Buffer.");
	        }
	        const encoder = new TextEncoder();
	        const timestampNumber = Math.floor(timestamp.getTime() / 1000);
	        const toSign = encoder.encode(`${msgId}.${timestampNumber}.${payload}`);
	        const expectedSignature = base64.encode(sha256.hmac(this.key, toSign));
	        return `v1,${expectedSignature}`;
	    }
	    verifyTimestamp(timestampHeader) {
	        const now = Math.floor(Date.now() / 1000);
	        const timestamp = parseInt(timestampHeader, 10);
	        if (isNaN(timestamp)) {
	            throw new WebhookVerificationError("Invalid Signature Headers");
	        }
	        if (now - timestamp > WEBHOOK_TOLERANCE_IN_SECONDS) {
	            throw new WebhookVerificationError("Message timestamp too old");
	        }
	        if (timestamp > now + WEBHOOK_TOLERANCE_IN_SECONDS) {
	            throw new WebhookVerificationError("Message timestamp too new");
	        }
	        return new Date(timestamp * 1000);
	    }
	}
	dist.Webhook = Webhook;
	Webhook.prefix = "whsec_";
	
	return dist;
}

var distExports = requireDist();

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookBenefitCreatedPayload$inboundSchema = objectType({
    type: literalType("benefit.created"),
    data: Benefit$inboundSchema,
});
/** @internal */
const WebhookBenefitCreatedPayload$outboundSchema = objectType({
    type: literalType("benefit.created"),
    data: Benefit$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookBenefitCreatedPayload$;
(function (WebhookBenefitCreatedPayload$) {
    /** @deprecated use `WebhookBenefitCreatedPayload$inboundSchema` instead. */
    WebhookBenefitCreatedPayload$.inboundSchema = WebhookBenefitCreatedPayload$inboundSchema;
    /** @deprecated use `WebhookBenefitCreatedPayload$outboundSchema` instead. */
    WebhookBenefitCreatedPayload$.outboundSchema = WebhookBenefitCreatedPayload$outboundSchema;
})(WebhookBenefitCreatedPayload$ || (WebhookBenefitCreatedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const BenefitGrantWebhookProperties$inboundSchema = unionType([
    BenefitGrantDiscordProperties$inboundSchema,
    BenefitGrantGitHubRepositoryProperties$inboundSchema,
    BenefitGrantDownloadablesProperties$inboundSchema,
    BenefitGrantLicenseKeysProperties$inboundSchema,
    BenefitGrantCustomProperties$inboundSchema,
]);
/** @internal */
const BenefitGrantWebhookProperties$outboundSchema = unionType([
    BenefitGrantDiscordProperties$outboundSchema,
    BenefitGrantGitHubRepositoryProperties$outboundSchema,
    BenefitGrantDownloadablesProperties$outboundSchema,
    BenefitGrantLicenseKeysProperties$outboundSchema,
    BenefitGrantCustomProperties$outboundSchema,
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var BenefitGrantWebhookProperties$;
(function (BenefitGrantWebhookProperties$) {
    /** @deprecated use `BenefitGrantWebhookProperties$inboundSchema` instead. */
    BenefitGrantWebhookProperties$.inboundSchema = BenefitGrantWebhookProperties$inboundSchema;
    /** @deprecated use `BenefitGrantWebhookProperties$outboundSchema` instead. */
    BenefitGrantWebhookProperties$.outboundSchema = BenefitGrantWebhookProperties$outboundSchema;
})(BenefitGrantWebhookProperties$ || (BenefitGrantWebhookProperties$ = {}));
/** @internal */
const PreviousProperties$inboundSchema = unionType([
    BenefitGrantDiscordProperties$inboundSchema,
    BenefitGrantGitHubRepositoryProperties$inboundSchema,
    BenefitGrantDownloadablesProperties$inboundSchema,
    BenefitGrantLicenseKeysProperties$inboundSchema,
    BenefitGrantCustomProperties$inboundSchema,
]);
/** @internal */
const PreviousProperties$outboundSchema = unionType([
    BenefitGrantDiscordProperties$outboundSchema,
    BenefitGrantGitHubRepositoryProperties$outboundSchema,
    BenefitGrantDownloadablesProperties$outboundSchema,
    BenefitGrantLicenseKeysProperties$outboundSchema,
    BenefitGrantCustomProperties$outboundSchema,
]);
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var PreviousProperties$;
(function (PreviousProperties$) {
    /** @deprecated use `PreviousProperties$inboundSchema` instead. */
    PreviousProperties$.inboundSchema = PreviousProperties$inboundSchema;
    /** @deprecated use `PreviousProperties$outboundSchema` instead. */
    PreviousProperties$.outboundSchema = PreviousProperties$outboundSchema;
})(PreviousProperties$ || (PreviousProperties$ = {}));
/** @internal */
const BenefitGrantWebhook$inboundSchema = objectType({
    created_at: stringType().datetime({ offset: true }).transform(v => new Date(v)),
    modified_at: nullableType(stringType().datetime({ offset: true }).transform(v => new Date(v))),
    id: stringType(),
    granted_at: nullableType(stringType().datetime({ offset: true }).transform(v => new Date(v))).optional(),
    is_granted: booleanType(),
    revoked_at: nullableType(stringType().datetime({ offset: true }).transform(v => new Date(v))).optional(),
    is_revoked: booleanType(),
    subscription_id: nullableType(stringType()),
    order_id: nullableType(stringType()),
    customer_id: stringType(),
    benefit_id: stringType(),
    error: nullableType(BenefitGrantError$inboundSchema).optional(),
    customer: Customer$inboundSchema,
    properties: unionType([
        BenefitGrantDiscordProperties$inboundSchema,
        BenefitGrantGitHubRepositoryProperties$inboundSchema,
        BenefitGrantDownloadablesProperties$inboundSchema,
        BenefitGrantLicenseKeysProperties$inboundSchema,
        BenefitGrantCustomProperties$inboundSchema,
    ]),
    benefit: Benefit$inboundSchema,
    previous_properties: nullableType(unionType([
        BenefitGrantDiscordProperties$inboundSchema,
        BenefitGrantGitHubRepositoryProperties$inboundSchema,
        BenefitGrantDownloadablesProperties$inboundSchema,
        BenefitGrantLicenseKeysProperties$inboundSchema,
        BenefitGrantCustomProperties$inboundSchema,
    ])).optional(),
}).transform((v) => {
    return remap(v, {
        "created_at": "createdAt",
        "modified_at": "modifiedAt",
        "granted_at": "grantedAt",
        "is_granted": "isGranted",
        "revoked_at": "revokedAt",
        "is_revoked": "isRevoked",
        "subscription_id": "subscriptionId",
        "order_id": "orderId",
        "customer_id": "customerId",
        "benefit_id": "benefitId",
        "previous_properties": "previousProperties",
    });
});
/** @internal */
const BenefitGrantWebhook$outboundSchema = objectType({
    createdAt: dateType().transform(v => v.toISOString()),
    modifiedAt: nullableType(dateType().transform(v => v.toISOString())),
    id: stringType(),
    grantedAt: nullableType(dateType().transform(v => v.toISOString())).optional(),
    isGranted: booleanType(),
    revokedAt: nullableType(dateType().transform(v => v.toISOString())).optional(),
    isRevoked: booleanType(),
    subscriptionId: nullableType(stringType()),
    orderId: nullableType(stringType()),
    customerId: stringType(),
    benefitId: stringType(),
    error: nullableType(BenefitGrantError$outboundSchema).optional(),
    customer: Customer$outboundSchema,
    properties: unionType([
        BenefitGrantDiscordProperties$outboundSchema,
        BenefitGrantGitHubRepositoryProperties$outboundSchema,
        BenefitGrantDownloadablesProperties$outboundSchema,
        BenefitGrantLicenseKeysProperties$outboundSchema,
        BenefitGrantCustomProperties$outboundSchema,
    ]),
    benefit: Benefit$outboundSchema,
    previousProperties: nullableType(unionType([
        BenefitGrantDiscordProperties$outboundSchema,
        BenefitGrantGitHubRepositoryProperties$outboundSchema,
        BenefitGrantDownloadablesProperties$outboundSchema,
        BenefitGrantLicenseKeysProperties$outboundSchema,
        BenefitGrantCustomProperties$outboundSchema,
    ])).optional(),
}).transform((v) => {
    return remap(v, {
        createdAt: "created_at",
        modifiedAt: "modified_at",
        grantedAt: "granted_at",
        isGranted: "is_granted",
        revokedAt: "revoked_at",
        isRevoked: "is_revoked",
        subscriptionId: "subscription_id",
        orderId: "order_id",
        customerId: "customer_id",
        benefitId: "benefit_id",
        previousProperties: "previous_properties",
    });
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var BenefitGrantWebhook$;
(function (BenefitGrantWebhook$) {
    /** @deprecated use `BenefitGrantWebhook$inboundSchema` instead. */
    BenefitGrantWebhook$.inboundSchema = BenefitGrantWebhook$inboundSchema;
    /** @deprecated use `BenefitGrantWebhook$outboundSchema` instead. */
    BenefitGrantWebhook$.outboundSchema = BenefitGrantWebhook$outboundSchema;
})(BenefitGrantWebhook$ || (BenefitGrantWebhook$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookBenefitGrantCreatedPayload$inboundSchema = objectType({
    type: literalType("benefit_grant.created"),
    data: BenefitGrantWebhook$inboundSchema,
});
/** @internal */
const WebhookBenefitGrantCreatedPayload$outboundSchema = objectType({
    type: literalType("benefit_grant.created"),
    data: BenefitGrantWebhook$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookBenefitGrantCreatedPayload$;
(function (WebhookBenefitGrantCreatedPayload$) {
    /** @deprecated use `WebhookBenefitGrantCreatedPayload$inboundSchema` instead. */
    WebhookBenefitGrantCreatedPayload$.inboundSchema = WebhookBenefitGrantCreatedPayload$inboundSchema;
    /** @deprecated use `WebhookBenefitGrantCreatedPayload$outboundSchema` instead. */
    WebhookBenefitGrantCreatedPayload$.outboundSchema = WebhookBenefitGrantCreatedPayload$outboundSchema;
})(WebhookBenefitGrantCreatedPayload$ || (WebhookBenefitGrantCreatedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookBenefitGrantRevokedPayload$inboundSchema = objectType({
    type: literalType("benefit_grant.revoked"),
    data: BenefitGrantWebhook$inboundSchema,
});
/** @internal */
const WebhookBenefitGrantRevokedPayload$outboundSchema = objectType({
    type: literalType("benefit_grant.revoked"),
    data: BenefitGrantWebhook$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookBenefitGrantRevokedPayload$;
(function (WebhookBenefitGrantRevokedPayload$) {
    /** @deprecated use `WebhookBenefitGrantRevokedPayload$inboundSchema` instead. */
    WebhookBenefitGrantRevokedPayload$.inboundSchema = WebhookBenefitGrantRevokedPayload$inboundSchema;
    /** @deprecated use `WebhookBenefitGrantRevokedPayload$outboundSchema` instead. */
    WebhookBenefitGrantRevokedPayload$.outboundSchema = WebhookBenefitGrantRevokedPayload$outboundSchema;
})(WebhookBenefitGrantRevokedPayload$ || (WebhookBenefitGrantRevokedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookBenefitGrantUpdatedPayload$inboundSchema = objectType({
    type: literalType("benefit_grant.updated"),
    data: BenefitGrantWebhook$inboundSchema,
});
/** @internal */
const WebhookBenefitGrantUpdatedPayload$outboundSchema = objectType({
    type: literalType("benefit_grant.updated"),
    data: BenefitGrantWebhook$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookBenefitGrantUpdatedPayload$;
(function (WebhookBenefitGrantUpdatedPayload$) {
    /** @deprecated use `WebhookBenefitGrantUpdatedPayload$inboundSchema` instead. */
    WebhookBenefitGrantUpdatedPayload$.inboundSchema = WebhookBenefitGrantUpdatedPayload$inboundSchema;
    /** @deprecated use `WebhookBenefitGrantUpdatedPayload$outboundSchema` instead. */
    WebhookBenefitGrantUpdatedPayload$.outboundSchema = WebhookBenefitGrantUpdatedPayload$outboundSchema;
})(WebhookBenefitGrantUpdatedPayload$ || (WebhookBenefitGrantUpdatedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookBenefitGrantCycledPayload$inboundSchema = objectType({
    type: literalType("benefit_grant.cycled"),
    data: BenefitGrantWebhook$inboundSchema,
});
/** @internal */
const WebhookBenefitGrantCycledPayload$outboundSchema = objectType({
    type: literalType("benefit_grant.cycled"),
    data: BenefitGrantWebhook$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookBenefitGrantCycledPayload$;
(function (WebhookBenefitGrantCycledPayload$) {
    /** @deprecated use `WebhookBenefitGrantCycledPayload$inboundSchema` instead. */
    WebhookBenefitGrantCycledPayload$.inboundSchema = WebhookBenefitGrantCycledPayload$inboundSchema;
    /** @deprecated use `WebhookBenefitGrantCycledPayload$outboundSchema` instead. */
    WebhookBenefitGrantCycledPayload$.outboundSchema = WebhookBenefitGrantCycledPayload$outboundSchema;
})(WebhookBenefitGrantCycledPayload$ || (WebhookBenefitGrantCycledPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookBenefitUpdatedPayload$inboundSchema = objectType({
    type: literalType("benefit.updated"),
    data: Benefit$inboundSchema,
});
/** @internal */
const WebhookBenefitUpdatedPayload$outboundSchema = objectType({
    type: literalType("benefit.updated"),
    data: Benefit$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookBenefitUpdatedPayload$;
(function (WebhookBenefitUpdatedPayload$) {
    /** @deprecated use `WebhookBenefitUpdatedPayload$inboundSchema` instead. */
    WebhookBenefitUpdatedPayload$.inboundSchema = WebhookBenefitUpdatedPayload$inboundSchema;
    /** @deprecated use `WebhookBenefitUpdatedPayload$outboundSchema` instead. */
    WebhookBenefitUpdatedPayload$.outboundSchema = WebhookBenefitUpdatedPayload$outboundSchema;
})(WebhookBenefitUpdatedPayload$ || (WebhookBenefitUpdatedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookCheckoutCreatedPayload$inboundSchema = objectType({
    type: literalType("checkout.created"),
    data: Checkout$inboundSchema,
});
/** @internal */
const WebhookCheckoutCreatedPayload$outboundSchema = objectType({
    type: literalType("checkout.created"),
    data: Checkout$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookCheckoutCreatedPayload$;
(function (WebhookCheckoutCreatedPayload$) {
    /** @deprecated use `WebhookCheckoutCreatedPayload$inboundSchema` instead. */
    WebhookCheckoutCreatedPayload$.inboundSchema = WebhookCheckoutCreatedPayload$inboundSchema;
    /** @deprecated use `WebhookCheckoutCreatedPayload$outboundSchema` instead. */
    WebhookCheckoutCreatedPayload$.outboundSchema = WebhookCheckoutCreatedPayload$outboundSchema;
})(WebhookCheckoutCreatedPayload$ || (WebhookCheckoutCreatedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookCheckoutUpdatedPayload$inboundSchema = objectType({
    type: literalType("checkout.updated"),
    data: Checkout$inboundSchema,
});
/** @internal */
const WebhookCheckoutUpdatedPayload$outboundSchema = objectType({
    type: literalType("checkout.updated"),
    data: Checkout$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookCheckoutUpdatedPayload$;
(function (WebhookCheckoutUpdatedPayload$) {
    /** @deprecated use `WebhookCheckoutUpdatedPayload$inboundSchema` instead. */
    WebhookCheckoutUpdatedPayload$.inboundSchema = WebhookCheckoutUpdatedPayload$inboundSchema;
    /** @deprecated use `WebhookCheckoutUpdatedPayload$outboundSchema` instead. */
    WebhookCheckoutUpdatedPayload$.outboundSchema = WebhookCheckoutUpdatedPayload$outboundSchema;
})(WebhookCheckoutUpdatedPayload$ || (WebhookCheckoutUpdatedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookOrderCreatedPayload$inboundSchema = objectType({
    type: literalType("order.created"),
    data: Order$inboundSchema,
});
/** @internal */
const WebhookOrderCreatedPayload$outboundSchema = objectType({
    type: literalType("order.created"),
    data: Order$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookOrderCreatedPayload$;
(function (WebhookOrderCreatedPayload$) {
    /** @deprecated use `WebhookOrderCreatedPayload$inboundSchema` instead. */
    WebhookOrderCreatedPayload$.inboundSchema = WebhookOrderCreatedPayload$inboundSchema;
    /** @deprecated use `WebhookOrderCreatedPayload$outboundSchema` instead. */
    WebhookOrderCreatedPayload$.outboundSchema = WebhookOrderCreatedPayload$outboundSchema;
})(WebhookOrderCreatedPayload$ || (WebhookOrderCreatedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookOrderRefundedPayload$inboundSchema = objectType({
    type: literalType("order.refunded"),
    data: Order$inboundSchema,
});
/** @internal */
const WebhookOrderRefundedPayload$outboundSchema = objectType({
    type: literalType("order.refunded"),
    data: Order$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookOrderRefundedPayload$;
(function (WebhookOrderRefundedPayload$) {
    /** @deprecated use `WebhookOrderRefundedPayload$inboundSchema` instead. */
    WebhookOrderRefundedPayload$.inboundSchema = WebhookOrderRefundedPayload$inboundSchema;
    /** @deprecated use `WebhookOrderRefundedPayload$outboundSchema` instead. */
    WebhookOrderRefundedPayload$.outboundSchema = WebhookOrderRefundedPayload$outboundSchema;
})(WebhookOrderRefundedPayload$ || (WebhookOrderRefundedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookOrderUpdatedPayload$inboundSchema = objectType({
    type: literalType("order.updated"),
    data: Order$inboundSchema,
});
/** @internal */
const WebhookOrderUpdatedPayload$outboundSchema = objectType({
    type: literalType("order.updated"),
    data: Order$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookOrderUpdatedPayload$;
(function (WebhookOrderUpdatedPayload$) {
    /** @deprecated use `WebhookOrderUpdatedPayload$inboundSchema` instead. */
    WebhookOrderUpdatedPayload$.inboundSchema = WebhookOrderUpdatedPayload$inboundSchema;
    /** @deprecated use `WebhookOrderUpdatedPayload$outboundSchema` instead. */
    WebhookOrderUpdatedPayload$.outboundSchema = WebhookOrderUpdatedPayload$outboundSchema;
})(WebhookOrderUpdatedPayload$ || (WebhookOrderUpdatedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookOrderPaidPayload$inboundSchema = objectType({
    type: literalType("order.paid"),
    data: Order$inboundSchema,
});
/** @internal */
const WebhookOrderPaidPayload$outboundSchema = objectType({
    type: literalType("order.paid"),
    data: Order$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookOrderPaidPayload$;
(function (WebhookOrderPaidPayload$) {
    /** @deprecated use `WebhookOrderPaidPayload$inboundSchema` instead. */
    WebhookOrderPaidPayload$.inboundSchema = WebhookOrderPaidPayload$inboundSchema;
    /** @deprecated use `WebhookOrderPaidPayload$outboundSchema` instead. */
    WebhookOrderPaidPayload$.outboundSchema = WebhookOrderPaidPayload$outboundSchema;
})(WebhookOrderPaidPayload$ || (WebhookOrderPaidPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookOrganizationUpdatedPayload$inboundSchema = objectType({
    type: literalType("organization.updated"),
    data: Organization$inboundSchema,
});
/** @internal */
const WebhookOrganizationUpdatedPayload$outboundSchema = objectType({
    type: literalType("organization.updated"),
    data: Organization$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookOrganizationUpdatedPayload$;
(function (WebhookOrganizationUpdatedPayload$) {
    /** @deprecated use `WebhookOrganizationUpdatedPayload$inboundSchema` instead. */
    WebhookOrganizationUpdatedPayload$.inboundSchema = WebhookOrganizationUpdatedPayload$inboundSchema;
    /** @deprecated use `WebhookOrganizationUpdatedPayload$outboundSchema` instead. */
    WebhookOrganizationUpdatedPayload$.outboundSchema = WebhookOrganizationUpdatedPayload$outboundSchema;
})(WebhookOrganizationUpdatedPayload$ || (WebhookOrganizationUpdatedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookProductCreatedPayload$inboundSchema = objectType({
    type: literalType("product.created"),
    data: Product$inboundSchema,
});
/** @internal */
const WebhookProductCreatedPayload$outboundSchema = objectType({
    type: literalType("product.created"),
    data: Product$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookProductCreatedPayload$;
(function (WebhookProductCreatedPayload$) {
    /** @deprecated use `WebhookProductCreatedPayload$inboundSchema` instead. */
    WebhookProductCreatedPayload$.inboundSchema = WebhookProductCreatedPayload$inboundSchema;
    /** @deprecated use `WebhookProductCreatedPayload$outboundSchema` instead. */
    WebhookProductCreatedPayload$.outboundSchema = WebhookProductCreatedPayload$outboundSchema;
})(WebhookProductCreatedPayload$ || (WebhookProductCreatedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookProductUpdatedPayload$inboundSchema = objectType({
    type: literalType("product.updated"),
    data: Product$inboundSchema,
});
/** @internal */
const WebhookProductUpdatedPayload$outboundSchema = objectType({
    type: literalType("product.updated"),
    data: Product$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookProductUpdatedPayload$;
(function (WebhookProductUpdatedPayload$) {
    /** @deprecated use `WebhookProductUpdatedPayload$inboundSchema` instead. */
    WebhookProductUpdatedPayload$.inboundSchema = WebhookProductUpdatedPayload$inboundSchema;
    /** @deprecated use `WebhookProductUpdatedPayload$outboundSchema` instead. */
    WebhookProductUpdatedPayload$.outboundSchema = WebhookProductUpdatedPayload$outboundSchema;
})(WebhookProductUpdatedPayload$ || (WebhookProductUpdatedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookRefundCreatedPayload$inboundSchema = objectType({
    type: literalType("refund.created"),
    data: Refund$inboundSchema,
});
/** @internal */
const WebhookRefundCreatedPayload$outboundSchema = objectType({
    type: literalType("refund.created"),
    data: Refund$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookRefundCreatedPayload$;
(function (WebhookRefundCreatedPayload$) {
    /** @deprecated use `WebhookRefundCreatedPayload$inboundSchema` instead. */
    WebhookRefundCreatedPayload$.inboundSchema = WebhookRefundCreatedPayload$inboundSchema;
    /** @deprecated use `WebhookRefundCreatedPayload$outboundSchema` instead. */
    WebhookRefundCreatedPayload$.outboundSchema = WebhookRefundCreatedPayload$outboundSchema;
})(WebhookRefundCreatedPayload$ || (WebhookRefundCreatedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookRefundUpdatedPayload$inboundSchema = objectType({
    type: literalType("refund.updated"),
    data: Refund$inboundSchema,
});
/** @internal */
const WebhookRefundUpdatedPayload$outboundSchema = objectType({
    type: literalType("refund.updated"),
    data: Refund$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookRefundUpdatedPayload$;
(function (WebhookRefundUpdatedPayload$) {
    /** @deprecated use `WebhookRefundUpdatedPayload$inboundSchema` instead. */
    WebhookRefundUpdatedPayload$.inboundSchema = WebhookRefundUpdatedPayload$inboundSchema;
    /** @deprecated use `WebhookRefundUpdatedPayload$outboundSchema` instead. */
    WebhookRefundUpdatedPayload$.outboundSchema = WebhookRefundUpdatedPayload$outboundSchema;
})(WebhookRefundUpdatedPayload$ || (WebhookRefundUpdatedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookSubscriptionActivePayload$inboundSchema = objectType({
    type: literalType("subscription.active"),
    data: Subscription$inboundSchema,
});
/** @internal */
const WebhookSubscriptionActivePayload$outboundSchema = objectType({
    type: literalType("subscription.active"),
    data: Subscription$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookSubscriptionActivePayload$;
(function (WebhookSubscriptionActivePayload$) {
    /** @deprecated use `WebhookSubscriptionActivePayload$inboundSchema` instead. */
    WebhookSubscriptionActivePayload$.inboundSchema = WebhookSubscriptionActivePayload$inboundSchema;
    /** @deprecated use `WebhookSubscriptionActivePayload$outboundSchema` instead. */
    WebhookSubscriptionActivePayload$.outboundSchema = WebhookSubscriptionActivePayload$outboundSchema;
})(WebhookSubscriptionActivePayload$ || (WebhookSubscriptionActivePayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookSubscriptionCanceledPayload$inboundSchema = objectType({
    type: literalType("subscription.canceled"),
    data: Subscription$inboundSchema,
});
/** @internal */
const WebhookSubscriptionCanceledPayload$outboundSchema = objectType({
    type: literalType("subscription.canceled"),
    data: Subscription$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookSubscriptionCanceledPayload$;
(function (WebhookSubscriptionCanceledPayload$) {
    /** @deprecated use `WebhookSubscriptionCanceledPayload$inboundSchema` instead. */
    WebhookSubscriptionCanceledPayload$.inboundSchema = WebhookSubscriptionCanceledPayload$inboundSchema;
    /** @deprecated use `WebhookSubscriptionCanceledPayload$outboundSchema` instead. */
    WebhookSubscriptionCanceledPayload$.outboundSchema = WebhookSubscriptionCanceledPayload$outboundSchema;
})(WebhookSubscriptionCanceledPayload$ || (WebhookSubscriptionCanceledPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookSubscriptionCreatedPayload$inboundSchema = objectType({
    type: literalType("subscription.created"),
    data: Subscription$inboundSchema,
});
/** @internal */
const WebhookSubscriptionCreatedPayload$outboundSchema = objectType({
    type: literalType("subscription.created"),
    data: Subscription$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookSubscriptionCreatedPayload$;
(function (WebhookSubscriptionCreatedPayload$) {
    /** @deprecated use `WebhookSubscriptionCreatedPayload$inboundSchema` instead. */
    WebhookSubscriptionCreatedPayload$.inboundSchema = WebhookSubscriptionCreatedPayload$inboundSchema;
    /** @deprecated use `WebhookSubscriptionCreatedPayload$outboundSchema` instead. */
    WebhookSubscriptionCreatedPayload$.outboundSchema = WebhookSubscriptionCreatedPayload$outboundSchema;
})(WebhookSubscriptionCreatedPayload$ || (WebhookSubscriptionCreatedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookSubscriptionRevokedPayload$inboundSchema = objectType({
    type: literalType("subscription.revoked"),
    data: Subscription$inboundSchema,
});
/** @internal */
const WebhookSubscriptionRevokedPayload$outboundSchema = objectType({
    type: literalType("subscription.revoked"),
    data: Subscription$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookSubscriptionRevokedPayload$;
(function (WebhookSubscriptionRevokedPayload$) {
    /** @deprecated use `WebhookSubscriptionRevokedPayload$inboundSchema` instead. */
    WebhookSubscriptionRevokedPayload$.inboundSchema = WebhookSubscriptionRevokedPayload$inboundSchema;
    /** @deprecated use `WebhookSubscriptionRevokedPayload$outboundSchema` instead. */
    WebhookSubscriptionRevokedPayload$.outboundSchema = WebhookSubscriptionRevokedPayload$outboundSchema;
})(WebhookSubscriptionRevokedPayload$ || (WebhookSubscriptionRevokedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookSubscriptionUncanceledPayload$inboundSchema = objectType({
    type: literalType("subscription.uncanceled"),
    data: Subscription$inboundSchema,
});
/** @internal */
const WebhookSubscriptionUncanceledPayload$outboundSchema = objectType({
    type: literalType("subscription.uncanceled"),
    data: Subscription$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookSubscriptionUncanceledPayload$;
(function (WebhookSubscriptionUncanceledPayload$) {
    /** @deprecated use `WebhookSubscriptionUncanceledPayload$inboundSchema` instead. */
    WebhookSubscriptionUncanceledPayload$.inboundSchema = WebhookSubscriptionUncanceledPayload$inboundSchema;
    /** @deprecated use `WebhookSubscriptionUncanceledPayload$outboundSchema` instead. */
    WebhookSubscriptionUncanceledPayload$.outboundSchema = WebhookSubscriptionUncanceledPayload$outboundSchema;
})(WebhookSubscriptionUncanceledPayload$ || (WebhookSubscriptionUncanceledPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookSubscriptionUpdatedPayload$inboundSchema = objectType({
    type: literalType("subscription.updated"),
    data: Subscription$inboundSchema,
});
/** @internal */
const WebhookSubscriptionUpdatedPayload$outboundSchema = objectType({
    type: literalType("subscription.updated"),
    data: Subscription$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookSubscriptionUpdatedPayload$;
(function (WebhookSubscriptionUpdatedPayload$) {
    /** @deprecated use `WebhookSubscriptionUpdatedPayload$inboundSchema` instead. */
    WebhookSubscriptionUpdatedPayload$.inboundSchema = WebhookSubscriptionUpdatedPayload$inboundSchema;
    /** @deprecated use `WebhookSubscriptionUpdatedPayload$outboundSchema` instead. */
    WebhookSubscriptionUpdatedPayload$.outboundSchema = WebhookSubscriptionUpdatedPayload$outboundSchema;
})(WebhookSubscriptionUpdatedPayload$ || (WebhookSubscriptionUpdatedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookCustomerCreatedPayload$inboundSchema = objectType({
    type: literalType("customer.created"),
    data: Customer$inboundSchema,
});
/** @internal */
const WebhookCustomerCreatedPayload$outboundSchema = objectType({
    type: literalType("customer.created"),
    data: Customer$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookCustomerCreatedPayload$;
(function (WebhookCustomerCreatedPayload$) {
    /** @deprecated use `WebhookCustomerCreatedPayload$inboundSchema` instead. */
    WebhookCustomerCreatedPayload$.inboundSchema = WebhookCustomerCreatedPayload$inboundSchema;
    /** @deprecated use `WebhookCustomerCreatedPayload$outboundSchema` instead. */
    WebhookCustomerCreatedPayload$.outboundSchema = WebhookCustomerCreatedPayload$outboundSchema;
})(WebhookCustomerCreatedPayload$ || (WebhookCustomerCreatedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookCustomerUpdatedPayload$inboundSchema = objectType({
    type: literalType("customer.updated"),
    data: Customer$inboundSchema,
});
/** @internal */
const WebhookCustomerUpdatedPayload$outboundSchema = objectType({
    type: literalType("customer.updated"),
    data: Customer$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookCustomerUpdatedPayload$;
(function (WebhookCustomerUpdatedPayload$) {
    /** @deprecated use `WebhookCustomerUpdatedPayload$inboundSchema` instead. */
    WebhookCustomerUpdatedPayload$.inboundSchema = WebhookCustomerUpdatedPayload$inboundSchema;
    /** @deprecated use `WebhookCustomerUpdatedPayload$outboundSchema` instead. */
    WebhookCustomerUpdatedPayload$.outboundSchema = WebhookCustomerUpdatedPayload$outboundSchema;
})(WebhookCustomerUpdatedPayload$ || (WebhookCustomerUpdatedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookCustomerDeletedPayload$inboundSchema = objectType({
    type: literalType("customer.deleted"),
    data: Customer$inboundSchema,
});
/** @internal */
const WebhookCustomerDeletedPayload$outboundSchema = objectType({
    type: literalType("customer.deleted"),
    data: Customer$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookCustomerDeletedPayload$;
(function (WebhookCustomerDeletedPayload$) {
    /** @deprecated use `WebhookCustomerDeletedPayload$inboundSchema` instead. */
    WebhookCustomerDeletedPayload$.inboundSchema = WebhookCustomerDeletedPayload$inboundSchema;
    /** @deprecated use `WebhookCustomerDeletedPayload$outboundSchema` instead. */
    WebhookCustomerDeletedPayload$.outboundSchema = WebhookCustomerDeletedPayload$outboundSchema;
})(WebhookCustomerDeletedPayload$ || (WebhookCustomerDeletedPayload$ = {}));

/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */
/** @internal */
const WebhookCustomerStateChangedPayload$inboundSchema = objectType({
    type: literalType("customer.state_changed"),
    data: CustomerState$inboundSchema,
});
/** @internal */
const WebhookCustomerStateChangedPayload$outboundSchema = objectType({
    type: literalType("customer.state_changed"),
    data: CustomerState$outboundSchema,
});
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
var WebhookCustomerStateChangedPayload$;
(function (WebhookCustomerStateChangedPayload$) {
    /** @deprecated use `WebhookCustomerStateChangedPayload$inboundSchema` instead. */
    WebhookCustomerStateChangedPayload$.inboundSchema = WebhookCustomerStateChangedPayload$inboundSchema;
    /** @deprecated use `WebhookCustomerStateChangedPayload$outboundSchema` instead. */
    WebhookCustomerStateChangedPayload$.outboundSchema = WebhookCustomerStateChangedPayload$outboundSchema;
})(WebhookCustomerStateChangedPayload$ || (WebhookCustomerStateChangedPayload$ = {}));

class WebhookVerificationError extends Error {
    constructor(message) {
        super(message);
        this.message = message;
    }
}
const parseEvent = (parsed) => {
    try {
        switch (parsed.type) {
            case "customer.created":
                return WebhookCustomerCreatedPayload$inboundSchema.parse(parsed);
            case "customer.updated":
                return WebhookCustomerUpdatedPayload$inboundSchema.parse(parsed);
            case "customer.deleted":
                return WebhookCustomerDeletedPayload$inboundSchema.parse(parsed);
            case "customer.state_changed":
                return WebhookCustomerStateChangedPayload$inboundSchema.parse(parsed);
            case "benefit.created":
                return WebhookBenefitCreatedPayload$inboundSchema.parse(parsed);
            case "benefit_grant.created":
                return WebhookBenefitGrantCreatedPayload$inboundSchema.parse(parsed);
            case "benefit_grant.cycled":
                return WebhookBenefitGrantCycledPayload$inboundSchema.parse(parsed);
            case "benefit_grant.revoked":
                return WebhookBenefitGrantRevokedPayload$inboundSchema.parse(parsed);
            case "benefit_grant.updated":
                return WebhookBenefitGrantUpdatedPayload$inboundSchema.parse(parsed);
            case "benefit.updated":
                return WebhookBenefitUpdatedPayload$inboundSchema.parse(parsed);
            case "checkout.created":
                return WebhookCheckoutCreatedPayload$inboundSchema.parse(parsed);
            case "checkout.updated":
                return WebhookCheckoutUpdatedPayload$inboundSchema.parse(parsed);
            case "order.created":
                return WebhookOrderCreatedPayload$inboundSchema.parse(parsed);
            case "order.paid":
                return WebhookOrderPaidPayload$inboundSchema.parse(parsed);
            case "order.updated":
                return WebhookOrderUpdatedPayload$inboundSchema.parse(parsed);
            case "order.refunded":
                return WebhookOrderRefundedPayload$inboundSchema.parse(parsed);
            case "organization.updated":
                return WebhookOrganizationUpdatedPayload$inboundSchema.parse(parsed);
            case "product.created":
                return WebhookProductCreatedPayload$inboundSchema.parse(parsed);
            case "product.updated":
                return WebhookProductUpdatedPayload$inboundSchema.parse(parsed);
            case "refund.created":
                return WebhookRefundCreatedPayload$inboundSchema.parse(parsed);
            case "refund.updated":
                return WebhookRefundUpdatedPayload$inboundSchema.parse(parsed);
            case "subscription.active":
                return WebhookSubscriptionActivePayload$inboundSchema.parse(parsed);
            case "subscription.canceled":
                return WebhookSubscriptionCanceledPayload$inboundSchema.parse(parsed);
            case "subscription.created":
                return WebhookSubscriptionCreatedPayload$inboundSchema.parse(parsed);
            case "subscription.revoked":
                return WebhookSubscriptionRevokedPayload$inboundSchema.parse(parsed);
            case "subscription.uncanceled":
                return WebhookSubscriptionUncanceledPayload$inboundSchema.parse(parsed);
            case "subscription.updated":
                return WebhookSubscriptionUpdatedPayload$inboundSchema.parse(parsed);
            default:
                throw new SDKValidationError(`Unknown event type: ${parsed.type}`, parsed.type, parsed);
        }
    }
    catch (error) {
        throw new SDKValidationError("Failed to parse event", error, parsed);
    }
};
const validateEvent = (body, headers, secret) => {
    const base64Secret = Buffer.from(secret, "utf-8").toString("base64");
    const webhook = new distExports.Webhook(base64Secret);
    try {
        const parsed = webhook.verify(body, headers);
        return parseEvent(parsed);
    }
    catch (error) {
        if (error instanceof distExports.WebhookVerificationError) {
            throw new WebhookVerificationError(error.message);
        }
        throw error;
    }
};

// src/checkout/checkout.ts
var Checkout = ({
  accessToken,
  successUrl,
  server,
  theme,
  includeCheckoutId = true
}) => {
  return async ({ url }) => {
    const polar = new Polar({
      accessToken,
      server
    });
    const products = url.searchParams.getAll("products");
    if (products.length === 0) {
      return Response.json(
        { error: "Missing products in query params" },
        { status: 400 }
      );
    }
    const success = successUrl ? new URL(successUrl) : void 0;
    if (success && includeCheckoutId) {
      success.searchParams.set("checkoutId", "{CHECKOUT_ID}");
    }
    try {
      const result = await polar.checkouts.create({
        products,
        successUrl: success ? decodeURI(success.toString()) : void 0,
        customerId: url.searchParams.get("customerId") ?? void 0,
        externalCustomerId: url.searchParams.get("customerExternalId") ?? void 0,
        customerEmail: url.searchParams.get("customerEmail") ?? void 0,
        customerName: url.searchParams.get("customerName") ?? void 0,
        customerBillingAddress: url.searchParams.has("customerBillingAddress") ? JSON.parse(url.searchParams.get("customerBillingAddress") ?? "{}") : void 0,
        customerTaxId: url.searchParams.get("customerTaxId") ?? void 0,
        customerIpAddress: url.searchParams.get("customerIpAddress") ?? void 0,
        customerMetadata: url.searchParams.has("customerMetadata") ? JSON.parse(url.searchParams.get("customerMetadata") ?? "{}") : void 0,
        allowDiscountCodes: url.searchParams.has("allowDiscountCodes") ? url.searchParams.get("allowDiscountCodes") === "true" : void 0,
        discountId: url.searchParams.get("discountId") ?? void 0,
        metadata: url.searchParams.has("metadata") ? JSON.parse(url.searchParams.get("metadata") ?? "{}") : void 0
      });
      const redirectUrl = new URL(result.url);
      if (theme) {
        redirectUrl.searchParams.set("theme", theme);
      }
      return Response.redirect(redirectUrl.toString());
    } catch (error) {
      console.error(error);
      return Response.json({ error: "Internal server error" }, { status: 500 });
    }
  };
};
var Webhooks = ({
  webhookSecret,
  onPayload,
  entitlements,
  ...eventHandlers
}) => {
  return async ({ request }) => {
    if (request.method !== "POST") {
      return Response.json({ message: "Method not allowed" }, { status: 405 });
    }
    const requestBody = await request.text();
    const webhookHeaders = {
      "webhook-id": request.headers.get("webhook-id") ?? "",
      "webhook-timestamp": request.headers.get("webhook-timestamp") ?? "",
      "webhook-signature": request.headers.get("webhook-signature") ?? ""
    };
    let webhookPayload;
    try {
      webhookPayload = validateEvent(
        requestBody,
        webhookHeaders,
        webhookSecret
      );
    } catch (error) {
      console.log(error);
      if (error instanceof WebhookVerificationError) {
        return Response.json({ received: false }, { status: 403 });
      }
      return Response.json({ error: "Internal server error" }, { status: 500 });
    }
    await handleWebhookPayload(webhookPayload, {
      webhookSecret,
      entitlements,
      onPayload,
      ...eventHandlers
    });
    return Response.json({ received: true });
  };
};

export { Checkout as C, Webhooks as W };
