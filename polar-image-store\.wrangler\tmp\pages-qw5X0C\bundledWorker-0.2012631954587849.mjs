var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

// _worker.js/index.js
import { renderers } from "./renderers.mjs";
import { c as createExports } from "./chunks/server_BulHO2l4.mjs";
import { manifest } from "./manifest_CZkauD_H.mjs";
globalThis.process ??= {};
globalThis.process.env ??= {};
var serverIslandMap = /* @__PURE__ */ new Map();
var _page0 = /* @__PURE__ */ __name(() => import("./pages/_image.astro.mjs"), "_page0");
var _page1 = /* @__PURE__ */ __name(() => import("./pages/about.astro.mjs"), "_page1");
var _page2 = /* @__PURE__ */ __name(() => import("./pages/api/checkout.astro.mjs"), "_page2");
var _page3 = /* @__PURE__ */ __name(() => import("./pages/api/products.astro.mjs"), "_page3");
var _page4 = /* @__PURE__ */ __name(() => import("./pages/api/search.astro.mjs"), "_page4");
var _page5 = /* @__PURE__ */ __name(() => import("./pages/api/webhooks.astro.mjs"), "_page5");
var _page6 = /* @__PURE__ */ __name(() => import("./pages/privacy.astro.mjs"), "_page6");
var _page7 = /* @__PURE__ */ __name(() => import("./pages/products/category/_category_.astro.mjs"), "_page7");
var _page8 = /* @__PURE__ */ __name(() => import("./pages/products/_slug_.astro.mjs"), "_page8");
var _page9 = /* @__PURE__ */ __name(() => import("./pages/products.astro.mjs"), "_page9");
var _page10 = /* @__PURE__ */ __name(() => import("./pages/success.astro.mjs"), "_page10");
var _page11 = /* @__PURE__ */ __name(() => import("./pages/terms.astro.mjs"), "_page11");
var _page12 = /* @__PURE__ */ __name(() => import("./pages/index.astro.mjs"), "_page12");
var pageMap = /* @__PURE__ */ new Map([
  ["node_modules/@astrojs/cloudflare/dist/entrypoints/image-endpoint.js", _page0],
  ["src/pages/about.astro", _page1],
  ["src/pages/api/checkout.ts", _page2],
  ["src/pages/api/products.ts", _page3],
  ["src/pages/api/search.ts", _page4],
  ["src/pages/api/webhooks.ts", _page5],
  ["src/pages/privacy.astro", _page6],
  ["src/pages/products/category/[category].astro", _page7],
  ["src/pages/products/[slug].astro", _page8],
  ["src/pages/products/index.astro", _page9],
  ["src/pages/success.astro", _page10],
  ["src/pages/terms.astro", _page11],
  ["src/pages/index.astro", _page12]
]);
var _manifest = Object.assign(manifest, {
  pageMap,
  serverIslandMap,
  renderers,
  actions: /* @__PURE__ */ __name(() => import("./_noop-actions.mjs"), "actions"),
  middleware: /* @__PURE__ */ __name(() => import("./_astro-internal_middleware.mjs"), "middleware")
});
var _exports = createExports(_manifest);
var __astrojsSsrVirtualEntry = _exports.default;
export {
  __astrojsSsrVirtualEntry as default,
  pageMap
};
//# sourceMappingURL=bundledWorker-0.2012631954587849.mjs.map
